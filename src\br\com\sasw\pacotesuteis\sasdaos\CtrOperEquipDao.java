/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.sasw.pacotesuteis.sasdaos;

import Dados.Consulta;
import Dados.Persistencia;
import br.com.sasw.pacotesuteis.sasbeans.CtrOperEquip;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CtrOperEquipDao {

    public List<CtrOperEquip> listarPosicaoContainers(String codFil, String codNivel, String codPessoa, Persistencia persistencia) throws Exception {
        try {
            List<CtrOperEquip> retorno = new ArrayList<>();
            String sql = " SELECT Clientes.nred, Clientes.latitude, Clientes.longitude,Clientes.Ende, Clientes.Bairro, \n"
                    + " Clientes.Cidade, Clientes.Estado, Clientes.CEP, Funcion.Nome, DATEDIFF(day,DtUltMov,convert(date,Getdate()))  Tempo_Dias, "
                    + " convert(date,CtrOperEquip.DtUltMov) DtUltMov_date,  CtrOperEquip.*,  \n"
                    + " Case when Isnull(CliFat.Limite,0) <= 0 then 10 else Isnull(CliFat.Limite,0) end Limite, "
                    + " Case when Isnull(Clientes.Limite,0) <= 0 then 10 else Isnull(Clientes.Limite,0) end LimiteLocal, Pedido.Solicitante, "
                    + " CliFat.NRed NredFat, FORMAT(DATEADD(day, Case when Isnull(CliFat.Limite,0) <= 0 then 10 else Isnull(CliFat.Limite,0) end, convert(date,CtrOperEquip.DtUltMov)), 'dd/MM/yyyy') dtPrevistoColeta, FORMAT(convert(date,CtrOperEquip.DtUltMov), 'dd/MM/yyyy') dtEntrega"
                    + " FROM CtrOperEquip \n"
                    + " LEFT JOIN Clientes ON Clientes.codigo = CtrOperEquip.codcli1 \n"
                    + "                  AND Clientes.codfil = CtrOperEquip.codfil \n"
                    + " LEFT JOIN Rt_Guias  on Rt_Guias.Guia = CtrOperEquip.Guia \n"
                    + "                    and Rt_Guias.Serie = CtrOperEquip.Serie \n"
                    + " LEFT JOIN Os_Vig  on OS_Vig.OS = Rt_Guias.OS \n"
                    + "                  and OS_Vig.CodFil = CtrOperEquip.CodFil \n"
                    + " LEFT JOIN Clientes CliFat  on CliFat.Codigo = OS_Vig.CliFat \n"
                    + "                          and CliFat.CodFil = OS_Vig.CodFil \n"
                    + " LEFT JOIN Pedido  on Pedido.SeqRota = CtrOperEquip.SeqRota \n"
                    + "                  and Pedido.Parada = CtrOperEquip.Parada \n"
                    + " LEFT JOIN Escala  on Escala.SeqRota = CtrOperEquip.SeqRota \n"
                    + " LEFT JOIN Funcion  on Funcion.Matr  = Escala.MatrMot ";
            if (!codNivel.equals("9")) {
                sql += " WHERE CtrOperEquip.Situacao = 'A' ";
                if (!codFil.equals("")) {
                    sql += " CtrOperEquip.codfil = ? and ";                            
                }
                sql += " Clientes.Codigo in ( Select b.Cliente from PessoaCliAut a \n"
                        + "                       Left Join OS_Vig b   on a.CodCli = b.CliFat \n"
                        + "                                           and a.CodFil = b.CodFil \n"
                        + "                       where a.CodFil = CtrOperEquip.CodFil \n"
                        + "                         and a.Codigo = ? \n"
                        + "                         and a.Flag_Excl <> '*') \n"
                        + " and CtrOperEquip.Situacao = 'A' ";
            }

            sql += " ORDER BY Tempo_Dias DESC \n ";

            Consulta consulta = new Consulta(sql, persistencia);

            if (!codNivel.equals("9")) {
                if (!codFil.equals("")) {
                    consulta.setString(codFil);
                }

                consulta.setString(codPessoa);
            }

            consulta.select();
            CtrOperEquip ctrOperEquip;
            while (consulta.Proximo()) {
                ctrOperEquip = new CtrOperEquip();
                ctrOperEquip.setIDEquip(consulta.getString("IDEquip"));
                ctrOperEquip.setDtUltMov(consulta.getString("DtUltMov_date"));
                ctrOperEquip.setTempoDias(consulta.getString("Tempo_Dias"));
                ctrOperEquip.setSeqRota(consulta.getString("SeqRota"));
                ctrOperEquip.setParada(consulta.getString("Parada"));
                ctrOperEquip.setER(consulta.getString("ER"));
                ctrOperEquip.setCodCli1(consulta.getString("CodCli1"));
                ctrOperEquip.setHora1(consulta.getString("Hora1"));
                ctrOperEquip.setHrcheg(consulta.getString("Hrcheg"));
                ctrOperEquip.setHrSaida(consulta.getString("HrSaida"));
                ctrOperEquip.setTempoespera(consulta.getString("Tempoespera"));
                ctrOperEquip.setGuia(consulta.getString("Guia"));
                ctrOperEquip.setSerie(consulta.getString("Serie"));
                ctrOperEquip.setOperador(consulta.getString("Operador"));
                ctrOperEquip.setDt_alter(consulta.getString("Dt_alter"));
                ctrOperEquip.setHr_alter(consulta.getString("Hr_alter"));
                ctrOperEquip.setLatitude(consulta.getString("latitude"));
                ctrOperEquip.setLongitude(consulta.getString("longitude"));
                ctrOperEquip.setNred(consulta.getString("nred"));

                ctrOperEquip.setEnde(consulta.getString("Ende"));
                ctrOperEquip.setBairro(consulta.getString("Bairro"));
                ctrOperEquip.setCidade(consulta.getString("Cidade"));
                ctrOperEquip.setUf(consulta.getString("Estado"));
                ctrOperEquip.setCep(consulta.getString("CEP"));
                ctrOperEquip.setMotorista(consulta.getString("Nome"));
                ctrOperEquip.setLimite(consulta.getString("Limite"));
                ctrOperEquip.setLimiteLocal(consulta.getString("LimiteLocal"));
                ctrOperEquip.setNredFat(consulta.getString("NredFat"));
                ctrOperEquip.setDataPrevistaColeta(consulta.getString("dtPrevistoColeta"));
                ctrOperEquip.setDataEntrega(consulta.getString("dtEntrega"));
                ctrOperEquip.setSolicitante(consulta.getString("Solicitante"));
                ctrOperEquip.setTipoEquip(consulta.getString("TipoEquip"));
                retorno.add(ctrOperEquip);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CtrOperEquipDao.listarPosicaoContainers - " + e.getMessage() + "\r\n"
                    + " SELECT Clientes.latitude, Clientes.longitude, CtrOperEquip.* \n"
                    + " FROM CtrOperEquip \n"
                    + " LEFT JOIN Clientes ON Clientes.codigo = CtrOperEquip.codcli1\n"
                    + "                  AND Clientes.codfil = CtrOperEquip.codfil\n");
        }
    }

    public List<CtrOperEquip> listarPosicaoContainersMarketing(String codPessoa, String dataPesquisa, String dataHoje, Persistencia persistencia) throws Exception {
        String sql = "";
        try {
            List<CtrOperEquip> retorno = new ArrayList<>();

            sql = "DECLARE @DataHoje DATE;"
                    + " SET @DataHoje = ?;\n"
                    + "  SELECT DISTINCT Clientes.nred, Clientes.latitude, Clientes.longitude,Clientes.Ende, Clientes.Bairro, \n"
                    + " Clientes.Cidade, Clientes.Estado, Clientes.CEP, Funcion.Nome, DATEDIFF(day,DtUltMov,convert(date,Getdate()))  Tempo_Dias, "
                    + " convert(date,CtrOperEquip.DtUltMov) DtUltMov_date,  CtrOperEquip.*,  \n"
                    + " Case when Isnull(CliFat.Limite,0) <= 0 then 10 else Isnull(CliFat.Limite,0) end Limite, "
                    + " Case when Isnull(Clientes.Limite,0) <= 0 then 10 else Isnull(Clientes.Limite,0) end LimiteLocal, Pedido.Solicitante, "
                    + " CliFat.NRed NredFat, FORMAT(DATEADD(day, Case when Isnull(CliFat.Limite,0) <= 0 then 10 else Isnull(CliFat.Limite,0) end, convert(date,CtrOperEquip.DtUltMov)), 'dd/MM/yyyy') dtPrevistoColeta, FORMAT(convert(date,CtrOperEquip.DtUltMov), 'dd/MM/yyyy') dtEntrega,\n"
                    + " TabMarketing.QtdeDiasFimContrato, FORMAT(convert(date,TabMarketing.DtInicio), 'dd/MM/yyyy') DtInicio, FORMAT(convert(date,TabMarketing.DtFinal), 'dd/MM/yyyy') DtFinal, CliMkt.NRed NredMkt\n"
                    + " FROM (SELECT \n"
                    + "       CONVERT(VARCHAR, CtrOperEquipMkt.IDEquip) IDEquip,\n"
                    + "       CtrOperEquipMkt.CodFil,\n"
                    + "       CtrOperEquipMkt.CodCli,\n"
                    + "       CtrOperEquipMkt.DtInicio,\n"
                    + "       CtrOperEquipMkt.DtFinal,\n"
                    + "       CASE WHEN CtrOperEquipMkt.DtFinal > @DataHoje THEN DATEDIFF(day, @DataHoje, CtrOperEquipMkt.DtFinal) ELSE '0' END QtdeDiasFimContrato\n"
                    + "       FROM PessoaCliAut \n"
                    + "       JOIN CtrOperEquipMkt\n"
                    + "         ON PessoaCliAut.CodCli = CtrOperEquipMkt.CodCli\n"
                    + "       JOIN Pessoa\n"
                    + "         ON PessoaCliAut.Codigo = Pessoa.Codigo\n"
                    + "       JOIN saspw\n"
                    + "         ON Pessoa.Codigo = saspw.Codigo\n"
                    + "       WHERE PessoaCliAut.Flag_excl   <> '*'\n"
                    + "       AND   Pessoa.Codigo             = ?\n"
                    + "       AND   CtrOperEquipMkt.DtFinal  >= ?) AS TabMarketing"
                    + " JOIN CtrOperEquip \n"
                    + "   ON TabMarketing.IDEquip = CtrOperEquip.IDEquip \n"
                    + " LEFT JOIN Clientes ON Clientes.codigo = CtrOperEquip.codcli1 \n"
                    + "                  AND Clientes.codfil = CtrOperEquip.codfil \n"
                    + " LEFT JOIN Rt_Guias  on Rt_Guias.Guia = CtrOperEquip.Guia \n"
                    + "                    and Rt_Guias.Serie = CtrOperEquip.Serie \n"
                    + " LEFT JOIN Os_Vig  on OS_Vig.OS = Rt_Guias.OS \n"
                    + "                  and OS_Vig.CodFil = CtrOperEquip.CodFil \n"
                    + " LEFT JOIN Clientes CliFat  on CliFat.Codigo = OS_Vig.CliFat \n"
                    + "                          and CliFat.CodFil = OS_Vig.CodFil \n"
                    + " LEFT JOIN Pedido  on Pedido.SeqRota = CtrOperEquip.SeqRota \n"
                    + "                  and Pedido.Parada = CtrOperEquip.Parada \n"
                    + " LEFT JOIN Escala  on Escala.SeqRota = CtrOperEquip.SeqRota \n"
                    + " LEFT JOIN Funcion  on Funcion.Matr  = Escala.MatrMot"
                    + " LEFT JOIN Clientes CliMkt  on TabMarketing.CodCli = CliMkt.Codigo \n"
                    + "                          and TabMarketing.CodFil = CliMkt.CodFil \n";

            sql += " ORDER BY Clientes.latitude DESC \n ";

            Consulta consulta = new Consulta(sql, persistencia);

            consulta.setString(dataHoje);
            consulta.setString(codPessoa);
            consulta.setString(dataPesquisa);

            consulta.select();
            CtrOperEquip ctrOperEquip;
            while (consulta.Proximo()) {
                ctrOperEquip = new CtrOperEquip();
                ctrOperEquip.setIDEquip(consulta.getString("IDEquip"));
                ctrOperEquip.setDtUltMov(consulta.getString("DtUltMov_date"));
                ctrOperEquip.setTempoDias(consulta.getString("Tempo_Dias"));
                ctrOperEquip.setSeqRota(consulta.getString("SeqRota"));
                ctrOperEquip.setParada(consulta.getString("Parada"));
                ctrOperEquip.setER(consulta.getString("ER"));
                ctrOperEquip.setCodCli1(consulta.getString("CodCli1"));
                ctrOperEquip.setHora1(consulta.getString("Hora1"));
                ctrOperEquip.setHrcheg(consulta.getString("Hrcheg"));
                ctrOperEquip.setHrSaida(consulta.getString("HrSaida"));
                ctrOperEquip.setTempoespera(consulta.getString("Tempoespera"));
                ctrOperEquip.setGuia(consulta.getString("Guia"));
                ctrOperEquip.setSerie(consulta.getString("Serie"));
                ctrOperEquip.setOperador(consulta.getString("Operador"));
                ctrOperEquip.setDt_alter(consulta.getString("Dt_alter"));
                ctrOperEquip.setHr_alter(consulta.getString("Hr_alter"));
                ctrOperEquip.setLatitude(consulta.getString("latitude"));
                ctrOperEquip.setLongitude(consulta.getString("longitude"));
                ctrOperEquip.setNred(consulta.getString("nred"));

                ctrOperEquip.setEnde(consulta.getString("Ende"));
                ctrOperEquip.setBairro(consulta.getString("Bairro"));
                ctrOperEquip.setCidade(consulta.getString("Cidade"));
                ctrOperEquip.setUf(consulta.getString("Estado"));
                ctrOperEquip.setCep(consulta.getString("CEP"));
                ctrOperEquip.setMotorista(consulta.getString("Nome"));
                ctrOperEquip.setLimite(consulta.getString("Limite"));
                ctrOperEquip.setLimiteLocal(consulta.getString("LimiteLocal"));
                ctrOperEquip.setNredFat(consulta.getString("NredFat"));

                ctrOperEquip.setDataPrevistaColeta(consulta.getString("dtPrevistoColeta"));
                ctrOperEquip.setDataEntrega(consulta.getString("dtEntrega"));
                ctrOperEquip.setSolicitante(consulta.getString("Solicitante"));

                ctrOperEquip.setQtdeDiasFimContrato(consulta.getString("QtdeDiasFimContrato"));
                ctrOperEquip.setDtInicio(consulta.getString("DtInicio"));
                ctrOperEquip.setDtFinal(consulta.getString("DtFinal"));
                ctrOperEquip.setNRedMkt(consulta.getString("NredMkt"));

                retorno.add(ctrOperEquip);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CtrOperEquipDao.listarPosicaoContainersMarketing - " + e.getMessage() + "\r\n"
                    + sql);
        }
    }

    public List<CtrOperEquip> listarPosicaoContainers(Persistencia persistencia) throws Exception {
        try {
            List<CtrOperEquip> retorno = new ArrayList<>();
            String sql = "SELECT Distinct Clientes.nred, Clientes.latitude, Clientes.longitude,Clientes.Ende, Clientes.Bairro, \n"
                    + " Clientes.Cidade, Clientes.Estado, Clientes.CEP, Funcion.Nome, DATEDIFF(day,DtUltMov,convert(date,Getdate()))  Tempo_Dias, "
                    + " convert(date,CtrOperEquip.DtUltMov) DtUltMov_date,  CtrOperEquip.*,  \n"
                    + " Case when Isnull(CliFat.Limite,0) <= 0 then 10 else Isnull(CliFat.Limite,0) end Limite, "
                    + " Case when Isnull(Clientes.Limite,0) <= 0 then 10 else Isnull(Clientes.Limite,0) end LimiteLocal, Pedido.Solicitante, "
                    + " CliFat.NRed NredFat, FORMAT(DATEADD(day, Case when Isnull(CliFat.Limite,0) <= 0 then 10 else Isnull(CliFat.Limite,0) end, convert(date,CtrOperEquip.DtUltMov)), 'dd/MM/yyyy') dtPrevistoColeta, FORMAT(convert(date,CtrOperEquip.DtUltMov), 'dd/MM/yyyy') dtEntrega, "
                    + " PstInspecao.CaminhoImagem, PstInspecao.CaminhoVideo "
                    + " FROM CtrOperEquip \n"
                    + " LEFT JOIN Clientes ON Clientes.codigo = CtrOperEquip.codcli1 \n"
                    + "                  AND Clientes.codfil = CtrOperEquip.codfil \n"
                    + " LEFT JOIN Rt_Guias  on Rt_Guias.Guia = CtrOperEquip.Guia \n"
                    + "                    and Rt_Guias.Serie = CtrOperEquip.Serie \n"
                     + " LEFT JOIN Rotas  on Rotas.Sequencia = Rt_guias.Sequencia \n"
                    + "                    and Rotas.CodFil = CtrOperEquip.Codfil \n"                    
                    + " LEFT JOIN Os_Vig  on OS_Vig.OS = Rt_Guias.OS \n"
                    + "                  and OS_Vig.CodFil = CtrOperEquip.CodFil \n"
                    + " LEFT JOIN Clientes CliFat  on CliFat.Codigo = OS_Vig.CliFat \n"
                    + "                          and CliFat.CodFil = OS_Vig.CodFil \n"
                    + " LEFT JOIN Pedido  on Pedido.SeqRota = CtrOperEquip.SeqRota \n"
                    + "                  and Pedido.Parada = CtrOperEquip.Parada \n"
                    + " LEFT JOIN Escala  on Escala.SeqRota = CtrOperEquip.SeqRota \n"
                    + " LEFT JOIN Funcion  on Funcion.Matr  = Escala.MatrMot\n"
                    + " Left JOin Inspecoes  on Inspecoes.Codigo = 2\n"
                    + " Left JOin inspecoesitens InspData on InspData.Codigo = Inspecoes.Codigo\n"
                    + "                                  and InspData.Sequencia = 1\n"
                    + " Left JOin inspecoesitens InspRota on InspRota.Codigo = Inspecoes.Codigo\n"
                    + "                                  and InspRota.Sequencia = 2\n"
                    + " Left JOin inspecoesitens InspParada on InspParada.Codigo = Inspecoes.Codigo\n"
                    + "                                    and InspParada.Sequencia = 3\n"
                    + " Left JOin inspecoesitens InspCacamba on InspCacamba.Codigo = Inspecoes.Codigo\n"
                    + "                                    and InspCacamba.Sequencia = 4\n"
                    + " Left Join PstInspecao PstInsRotaData on PstInsRotaData.CodInspecao = Inspecoes.Codigo "
                    + "                                     and PstInsRotaData.Codfil = Rotas.CodFil "
                    + " 		  		    and PstInsRotaData.pergunta = InspData.Pergunta "
                    + " 			            and convert(datetime, PstInsRotaData.Resposta, 103) = Rotas.Data "                    
                    + " Left Join PstInspecao PstInsRota on PstInsRota.CodInspecao = Inspecoes.Codigo "
                    + "                      and PstInsRota.Codfil = Rotas.CodFil\n"
                    + "			     and PstInsRota.Codigo = PstInsRotaData.Codigo \n"
                    + "			     and PstInsRota.pergunta = InspRota.Pergunta \n"
                    + "					  and PstInsRota.Resposta = Rotas.Rota \n"
                    + " Left Join PstInspecao PstInsParada on PstInsRota.CodInspecao = Inspecoes.Codigo \n"
                    + "                                   and PstInsParada.Codigo = PstInsRota.Codigo \n"
                    + "                                   and PstInsParada.Codfil = Rotas.CodFil \n"
                    + "					  and PstInsParada.pergunta = InspParada.Pergunta \n"
                    + "					  and PstInsParada.Resposta = Rt_Guias.Parada \n"
                    + " Left Join PstInspecao on PstInspecao.CodInspecao = Inspecoes.Codigo \n"
                    + "                       and PstInspecao.Codigo = PstInsParada.Codigo \n"
                    + "                       and PstInspecao.Codfil = Rotas.CodFil \n"
                    + "			      and PstInspecao.pergunta = InspCacamba.Pergunta \n"
                    + "			      and PstInspecao.Codigo = PstInsRota.Codigo \n"
                    + "			      and PstInspecao.Resposta = CtrOperEquip.IDEquip \n"
                    //+ " WHERE DATEDIFF(day,DtUltMov,convert(date,Getdate())) >= 300 \n"
                    + "  WHERE (CtrOperEquip.Situacao <> 'I' or CtrOperEquip.Situacao is null) "
                    + " ORDER BY Tempo_Dias DESC \n ";
            Consulta consulta = new Consulta(sql, persistencia);

            consulta.select();
            CtrOperEquip ctrOperEquip;
            while (consulta.Proximo()) {
                ctrOperEquip = new CtrOperEquip();
                ctrOperEquip.setIDEquip(consulta.getString("IDEquip"));
                ctrOperEquip.setDtUltMov(consulta.getString("DtUltMov_date"));
                ctrOperEquip.setTempoDias(consulta.getString("Tempo_Dias"));
                ctrOperEquip.setSeqRota(consulta.getString("SeqRota"));
                ctrOperEquip.setParada(consulta.getString("Parada"));
                ctrOperEquip.setER(consulta.getString("ER"));
                ctrOperEquip.setCodCli1(consulta.getString("CodCli1"));
                ctrOperEquip.setHora1(consulta.getString("Hora1"));
                ctrOperEquip.setHrcheg(consulta.getString("Hrcheg"));
                ctrOperEquip.setHrSaida(consulta.getString("HrSaida"));
                ctrOperEquip.setTempoespera(consulta.getString("Tempoespera"));
                ctrOperEquip.setGuia(consulta.getString("Guia"));
                ctrOperEquip.setSerie(consulta.getString("Serie"));
                ctrOperEquip.setOperador(consulta.getString("Operador"));
                ctrOperEquip.setDt_alter(consulta.getString("Dt_alter"));
                ctrOperEquip.setHr_alter(consulta.getString("Hr_alter"));
                ctrOperEquip.setLatitude(consulta.getString("latitude"));
                ctrOperEquip.setLongitude(consulta.getString("longitude"));
                ctrOperEquip.setNred(consulta.getString("nred"));

                ctrOperEquip.setEnde(consulta.getString("Ende"));
                ctrOperEquip.setBairro(consulta.getString("Bairro"));
                ctrOperEquip.setCidade(consulta.getString("Cidade"));
                ctrOperEquip.setUf(consulta.getString("Estado"));
                ctrOperEquip.setCep(consulta.getString("CEP"));
                ctrOperEquip.setMotorista(consulta.getString("Nome"));
                ctrOperEquip.setLimite(consulta.getString("Limite"));
                ctrOperEquip.setLimiteLocal(consulta.getString("LimiteLocal"));
                ctrOperEquip.setNredFat(consulta.getString("NredFat"));
                ctrOperEquip.setDataPrevistaColeta(consulta.getString("dtPrevistoColeta"));
                ctrOperEquip.setDataEntrega(consulta.getString("dtEntrega"));
                ctrOperEquip.setSolicitante(consulta.getString("Solicitante"));
                if (consulta.getString("CaminhoImagem") == null){
                  ctrOperEquip.setCaminhoImagem("SEM CAPTURA");   
                }else{
                   ctrOperEquip.setCaminhoImagem(consulta.getString("CaminhoImagem"));
                }
                if (consulta.getString("CaminhoVideo") == null){
                  ctrOperEquip.setCaminhoVideo("SEM CAPTURA");   
                }else{
                   ctrOperEquip.setCaminhoVideo(consulta.getString("CaminhoVideo"));
                }
                retorno.add(ctrOperEquip);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CtrOperEquipDao.listarPosicaoContainers - " + e.getMessage() + "\r\n"
                    + " SELECT Clientes.latitude, Clientes.longitude, CtrOperEquip.* \n"
                    + " FROM CtrOperEquip \n"
                    + " LEFT JOIN Clientes ON Clientes.codigo = CtrOperEquip.codcli1\n"
                    + "                  AND Clientes.codfil = CtrOperEquip.codfil\n");
        }
    }

    /**
     * Lista bairros distintos para filtro do mapa de containers
     * @param persistencia
     * @return Lista de bairros
     * @throws Exception
     */
    public List<String> listarBairrosContainers(Persistencia persistencia) throws Exception {
        try {
            List<String> retorno = new ArrayList<>();
            String sql = "SELECT distinct c.Bairro " +
                        "FROM equipamentos e " +
                        "left join CtrOperequip co on co.IDEquip = e.IDEquip " +
                        "join Clientes c on c.CodFil = co.CodFil and c.Codigo = co.CodCli1 " +
                        "where e.Situacao = 'A' and c.Latitude <> '' and c.Longitude <> '' and c.Bairro <> '' " +
                        "order by c.Bairro asc";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            while (consulta.Proximo()) {
                String bairro = consulta.getString("Bairro");
                if (bairro != null && !bairro.trim().isEmpty()) {
                    retorno.add(bairro);
                }
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CtrOperEquipDao.listarBairrosContainers - " + e.getMessage());
        }
    }

    /**
     * Lista solicitantes distintos para filtro do mapa de containers
     * @param persistencia
     * @return Lista de solicitantes
     * @throws Exception
     */
    public List<String> listarSolicitantesContainers(Persistencia persistencia) throws Exception {
        try {
            List<String> retorno = new ArrayList<>();
            String sql = "SELECT distinct p.Solicitante " +
                        "FROM equipamentos e " +
                        "left join CtrOperequip co on co.IDEquip = e.IDEquip " +
                        "join Clientes c on c.CodFil = co.CodFil and c.Codigo = co.CodCli1 " +
                        "left join Rt_Guias rtg on rtg.Guia = co.Guia and rtg.Serie = co.Serie " +
                        "left join OS_Vig ov on ov.OS = rtg.OS and ov.CodFil = co.CodFil " +
                        "left join clientes clifat on clifat.Codigo = ov.CliFat and clifat.CodFil = ov.CodFil " +
                        "left join Pedido p on p.SeqRota = co.SeqRota and p.Parada = co.Parada " +
                        "where e.Situacao = 'A' and c.Latitude <> '' and c.Longitude <> '' and p.Solicitante <> '' " +
                        "order by p.Solicitante asc";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            while (consulta.Proximo()) {
                String solicitante = consulta.getString("Solicitante");
                if (solicitante != null && !solicitante.trim().isEmpty()) {
                    retorno.add(solicitante);
                }
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CtrOperEquipDao.listarSolicitantesContainers - " + e.getMessage());
        }
    }

    /**
     * Lista clientes distintos para filtro do mapa de containers
     * @param persistencia
     * @return Lista de clientes
     * @throws Exception
     */
    public List<String> listarClientesContainers(Persistencia persistencia) throws Exception {
        try {
            List<String> retorno = new ArrayList<>();
            String sql = "SELECT distinct clifat.NRed " +
                        "FROM equipamentos e " +
                        "left join CtrOperequip co on co.IDEquip = e.IDEquip " +
                        "join Clientes c on c.CodFil = co.CodFil and c.Codigo = co.CodCli1 " +
                        "left join Rt_Guias rtg on rtg.Guia = co.Guia and rtg.Serie = co.Serie " +
                        "left join OS_Vig ov on ov.OS = rtg.OS and ov.CodFil = co.CodFil " +
                        "left join clientes clifat on clifat.Codigo = ov.CliFat and clifat.CodFil = ov.CodFil " +
                        "left join Pedido p on p.SeqRota = co.SeqRota and p.Parada = co.Parada " +
                        "where e.Situacao = 'A' and c.Latitude <> '' and c.Longitude <> '' and clifat.NRed <> '' " +
                        "order by clifat.NRed asc";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            while (consulta.Proximo()) {
                String cliente = consulta.getString("NRed");
                if (cliente != null && !cliente.trim().isEmpty()) {
                    retorno.add(cliente);
                }
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CtrOperEquipDao.listarClientesContainers - " + e.getMessage());
        }
    }

    /**
     * Lista clientes de serviço distintos para filtro do mapa de containers
     * @param persistencia
     * @return Lista de clientes de serviço
     * @throws Exception
     */
    public List<String> listarClientesServicoContainers(Persistencia persistencia) throws Exception {
        try {
            List<String> retorno = new ArrayList<>();
            String sql = "SELECT distinct c.NRed " +
                        "FROM equipamentos e " +
                        "left join CtrOperequip co on co.IDEquip = e.IDEquip " +
                        "join Clientes c on c.CodFil = co.CodFil and c.Codigo = co.CodCli1 " +
                        "where e.Situacao = 'A' and c.Latitude <> '' and c.Longitude <> '' and c.NRed <> '' " +
                        "order by c.NRed asc";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.select();

            while (consulta.Proximo()) {
                String clienteServico = consulta.getString("NRed");
                if (clienteServico != null && !clienteServico.trim().isEmpty()) {
                    retorno.add(clienteServico);
                }
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CtrOperEquipDao.listarClientesServicoContainers - " + e.getMessage());
        }
    }

    /**
     * Lista equipamentos com filtros dinâmicos para o mapa de containers
     * @param codFil
     * @param codNivel
     * @param codPessoa
     * @param filtros Map com os filtros a serem aplicados
     * @param persistencia
     * @return Lista de equipamentos filtrados
     * @throws Exception
     */
    public List<CtrOperEquip> listarPosicoesContainersFiltrado(String codFil, String codNivel, String codPessoa,
            Map<String, List<String>> filtros, Persistencia persistencia) throws Exception {
        try {
            List<CtrOperEquip> retorno = new ArrayList<>();

            // Query base - usar a mesma estrutura do método original
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT e.TipoEquip, c.nred, c.latitude, c.longitude, c.Ende, c.Bairro, ");
            sql.append("c.Cidade, c.Estado, c.CEP, Funcion.Nome, DATEDIFF(day,co.DtUltMov,convert(date,Getdate())) Tempo_Dias, ");
            sql.append("convert(date,co.DtUltMov) DtUltMov_date, co.*, ");
            sql.append("Case when Isnull(clifat.Limite,0) <= 0 then 10 else Isnull(clifat.Limite,0) end Limite, ");
            sql.append("Case when Isnull(c.Limite,0) <= 0 then 10 else Isnull(c.Limite,0) end LimiteLocal, p.Solicitante, ");
            sql.append("clifat.NRed NredFat, FORMAT(DATEADD(day, Case when Isnull(clifat.Limite,0) <= 0 then 10 else Isnull(clifat.Limite,0) end, convert(date,co.DtUltMov)), 'dd/MM/yyyy') dtPrevistoColeta, ");
            sql.append("FORMAT(convert(date,co.DtUltMov), 'dd/MM/yyyy') dtEntrega ");
            sql.append("FROM equipamentos e ");
            sql.append("left join CtrOperequip co on co.IDEquip = e.IDEquip ");
            sql.append("LEFT JOIN Clientes c ON c.codigo = co.codcli1 AND c.codfil = co.codfil ");
            sql.append("LEFT JOIN Rt_Guias rtg on rtg.Guia = co.Guia and rtg.Serie = co.Serie ");
            sql.append("LEFT JOIN Os_Vig ov on ov.OS = rtg.OS and ov.CodFil = co.CodFil ");
            sql.append("LEFT JOIN Clientes clifat on clifat.Codigo = ov.CliFat and clifat.CodFil = ov.CodFil ");
            sql.append("LEFT JOIN Pedido p on p.SeqRota = co.SeqRota and p.Parada = co.Parada ");
            sql.append("LEFT JOIN Escala on Escala.SeqRota = co.SeqRota ");
            sql.append("LEFT JOIN Funcion on Funcion.Matr = Escala.MatrMot ");
            sql.append("where e.Situacao = 'A' and c.Latitude <> '' and c.Longitude <> ''");

            List<Object> parametros = new ArrayList<>();

            // Aplicar filtros dinâmicos
            if (filtros != null) {
                // Filtro por bairros
                if (filtros.containsKey("bairros") && filtros.get("bairros") != null && !filtros.get("bairros").isEmpty()) {
                    List<String> bairros = filtros.get("bairros");
                    sql.append(" and c.Bairro IN (");
                    for (int i = 0; i < bairros.size(); i++) {
                        if (i > 0) sql.append(",");
                        sql.append("?");
                        parametros.add(bairros.get(i));
                    }
                    sql.append(")");
                }

                // Filtro por clientes
                if (filtros.containsKey("clientes") && filtros.get("clientes") != null && !filtros.get("clientes").isEmpty()) {
                    List<String> clientes = filtros.get("clientes");
                    sql.append(" and clifat.NRed IN (");
                    for (int i = 0; i < clientes.size(); i++) {
                        if (i > 0) sql.append(",");
                        sql.append("?");
                        parametros.add(clientes.get(i));
                    }
                    sql.append(")");
                }

                // Filtro por clientes de serviço
                if (filtros.containsKey("clientesServico") && filtros.get("clientesServico") != null && !filtros.get("clientesServico").isEmpty()) {
                    List<String> clientesServico = filtros.get("clientesServico");
                    sql.append(" and c.NRed IN (");
                    for (int i = 0; i < clientesServico.size(); i++) {
                        if (i > 0) sql.append(",");
                        sql.append("?");
                        parametros.add(clientesServico.get(i));
                    }
                    sql.append(")");
                }

                // Filtro por solicitantes
                if (filtros.containsKey("solicitantes") && filtros.get("solicitantes") != null && !filtros.get("solicitantes").isEmpty()) {
                    List<String> solicitantes = filtros.get("solicitantes");
                    sql.append(" and p.Solicitante IN (");
                    for (int i = 0; i < solicitantes.size(); i++) {
                        if (i > 0) sql.append(",");
                        sql.append("?");
                        parametros.add(solicitantes.get(i));
                    }
                    sql.append(")");
                }

                // Filtro por tipos de equipamento
                if (filtros.containsKey("tiposEquipamento") && filtros.get("tiposEquipamento") != null && !filtros.get("tiposEquipamento").isEmpty()) {
                    List<String> tipos = filtros.get("tiposEquipamento");
                    sql.append(" and e.TipoEquip IN (");
                    for (int i = 0; i < tipos.size(); i++) {
                        if (i > 0) sql.append(",");
                        sql.append("?");
                        parametros.add(tipos.get(i));
                    }
                    sql.append(")");
                }
            }

            Consulta consulta = new Consulta(sql.toString(), persistencia);

            // Definir parâmetros
            for (Object param : parametros) {
                consulta.setString(param.toString());
            }

            consulta.select();

            while (consulta.Proximo()) {
                CtrOperEquip ctrOperEquip = new CtrOperEquip();
                ctrOperEquip.setIDEquip(consulta.getString("IDEquip"));
                ctrOperEquip.setDtUltMov(consulta.getString("DtUltMov_date"));
                ctrOperEquip.setTempoDias(consulta.getString("Tempo_Dias"));
                ctrOperEquip.setSeqRota(consulta.getString("SeqRota"));
                ctrOperEquip.setParada(consulta.getString("Parada"));
                ctrOperEquip.setER(consulta.getString("ER"));
                ctrOperEquip.setCodCli1(consulta.getString("CodCli1"));
                ctrOperEquip.setHora1(consulta.getString("Hora1"));
                ctrOperEquip.setHrcheg(consulta.getString("Hrcheg"));
                ctrOperEquip.setHrSaida(consulta.getString("HrSaida"));
                ctrOperEquip.setTempoespera(consulta.getString("Tempoespera"));
                ctrOperEquip.setGuia(consulta.getString("Guia"));
                ctrOperEquip.setSerie(consulta.getString("Serie"));
                ctrOperEquip.setOperador(consulta.getString("Operador"));
                ctrOperEquip.setDt_alter(consulta.getString("Dt_alter"));
                ctrOperEquip.setHr_alter(consulta.getString("Hr_alter"));
                ctrOperEquip.setLatitude(consulta.getString("latitude"));
                ctrOperEquip.setLongitude(consulta.getString("longitude"));
                ctrOperEquip.setNred(consulta.getString("nred"));
                ctrOperEquip.setEnde(consulta.getString("Ende"));
                ctrOperEquip.setBairro(consulta.getString("Bairro"));
                ctrOperEquip.setCidade(consulta.getString("Cidade"));
                ctrOperEquip.setUf(consulta.getString("Estado"));
                ctrOperEquip.setCep(consulta.getString("CEP"));
                ctrOperEquip.setMotorista(consulta.getString("Nome"));
                ctrOperEquip.setLimite(consulta.getString("Limite"));
                ctrOperEquip.setLimiteLocal(consulta.getString("LimiteLocal"));
                ctrOperEquip.setNredFat(consulta.getString("NredFat"));
                ctrOperEquip.setDataPrevistaColeta(consulta.getString("dtPrevistoColeta"));
                ctrOperEquip.setDataEntrega(consulta.getString("dtEntrega"));
                ctrOperEquip.setSolicitante(consulta.getString("Solicitante"));
                ctrOperEquip.setTipoEquip(consulta.getString("TipoEquip"));

                retorno.add(ctrOperEquip);
            }
            consulta.close();
            return retorno;
        } catch (Exception e) {
            throw new Exception("CtrOperEquipDao.listarPosicoesContainersFiltrado - " + e.getMessage());
        }
    }

    public void processarCtrOperEquip(String Data, String CodFil, String HrAlter, Persistencia persistencia) throws Exception {
        String sql = "";
        String DtAlter = Data;

        try {
            // Deletar do Dia
            sql = "Delete from CtrOperEquip\n"
                    + " where CodFil    = ?\n"
                    + " and DtUltMov   >= ?;";

            Consulta consulta = new Consulta(sql, persistencia);
            consulta.setString(CodFil);
            consulta.setString(Data);

            consulta.delete();

            // Verificar os equipamentos
            sql = "Select IDEquip from CtrOperEquip";

            consulta = new Consulta(sql, persistencia);
            consulta.select();

            List<String> equipamentos = new ArrayList<>();

            while (consulta.Proximo()) {
                equipamentos.add(consulta.getString("IDEquip"));
            }

            // Consultar Dados
            sql = "Select CxfGuiasVol.Lacre IDEquip, Rotas.Data, Rotas.Sequencia SeqRota, Rotas.Rota, Rt_Perc.Parada, Rt_Perc.ER, Rt_perc.CodCli1,\n"
                    + "           Rt_Perc.NRed, Rt_Perc.Hora1, Rt_Perc.HrCheg, Rt_Perc.HrSaida, Rt_Perc.TempoEspera,Funcion.Nome_Guer Motorista, Veiculos.Numero,\n"
                    + "            Veiculos.Placa+'/'+Veiculos.UF_Placa Placa, VeiculosMod.Descricao Modelo, CxfGuiasVol.Guia, CxfGuiasVol.Serie\n"
                    + "            from Rt_perc\n"
                    + "            left join Rotas on Rotas.sequencia = Rt_Perc.Sequencia\n"
                    + "            left join Rt_Guias on Rt_guias.Sequencia = Rt_Perc.Sequencia\n"
                    + "                               and Rt_Guias.Parada = Rt_Perc.Parada\n"
                    + "            left join CxfGuiasVol on CxfGuiasVol.Guia = Rt_guias.Guia\n"
                    + "                                 and CxfGuiasVol.Serie = Rt_guias.Serie\n"
                    + "            left join Escala on Escala.SeqRota = Rotas.Sequencia\n"
                    + "            left join Funcion on Funcion.Matr = Escala.Matrmot\n"
                    + "            left join Veiculos on Veiculos.Numero = Escala.Veiculo\n"
                    + "            left join VeiculosMod on VeiculosMod.Codigo = Veiculos.Modelo\n"
                    + "            where Rotas.Data  >= ?\n"
                    + "              and Rotas.CodFil = ?\n"
                    + "              and CxfGuiasVol.Lacre is not null\n"
                    + "              and CxfGuiasVol.Lacre <> ''\n"
                    + "            order by CxfguiasVol.Lacre, Rotas.Data, Rt_Perc.Hora1;";

            consulta = new Consulta(sql, persistencia);
            consulta.setString(Data);
            consulta.setString(CodFil);

            consulta.select();

            // Percorrer equipamentos e processar
            while (consulta.Proximo()) {
                try {
                    if (equipamentos.size() > 0
                            && equipamentos.contains(new String(consulta.getString("IDEquip")))) {
                        // UPDATE
                        sql = "Update CtrOperEquip set\n"
                                + "CodFil      = ?,\n"
                                + "DtUltMov    = ?,\n"
                                + "TempoDias   = DateDiff(Day, '" + consulta.getString("Data").split(" ")[0] + "', GetDate()),\n"
                                + "SeqRota     = ?,\n"
                                + "Parada      = ?,\n"
                                + "ER          = ?,\n"
                                + "CodCli1     = ?,\n"
                                + "Hora1       = ?,\n"
                                + "HrCheg      = ?,\n"
                                + "HrSaida     = ?,\n"
                                + "TempoEspera = ?,\n"
                                + "Guia        = ?,\n"
                                + "Serie       = ?,\n"
                                + "Operador    = ?,\n"
                                + "Dt_Alter    = ?,\n"
                                + "Hr_alter    = ?\n"
                                + "where IDEquip = ?";

                        Consulta consulta2 = new Consulta(sql, persistencia);

                        consulta2.setString(CodFil);
                        consulta2.setString(consulta.getString("Data").split(" ")[0]);
                        consulta2.setInt(consulta.getInt("Seqrota"));
                        consulta2.setInt(consulta.getInt("Parada"));
                        consulta2.setString(consulta.getString("ER"));
                        consulta2.setString(consulta.getString("CodCli1"));
                        consulta2.setString(consulta.getString("Hora1"));
                        consulta2.setString(consulta.getString("HrCheg"));
                        consulta2.setString(consulta.getString("HrSaida"));
                        consulta2.setInt(consulta.getInt("TempoEspera"));
                        consulta2.setString(consulta.getString("Guia"));
                        consulta2.setString(consulta.getString("Serie"));
                        consulta2.setString("SATMOB");
                        consulta2.setString(DtAlter);
                        consulta2.setString(HrAlter);
                        consulta2.setString(consulta.getString("IDEquip"));

                        consulta2.update();

                    } else {
                        // INSERT
                        sql = "Insert into CtrOperEquip (IDEquip, CodFil, DtUltMov, TempoDias, SeqRota, Parada, ER, CodCli1, Hora1, HrCheg, HrSaida, TempoEspera,\n"
                                + "                      Guia, Serie, Operador, Dt_Alter, Hr_alter) Values (?,?,?,DateDiff(Day, '" + consulta.getString("Data").split(" ")[0] + "', GetDate()),?,?,?,?,?,?,?,?,?,?,?,?,?)";

                        Consulta consulta2 = new Consulta(sql, persistencia);

                        consulta2.setString(consulta.getString("IDEquip"));
                        consulta2.setString(CodFil);
                        consulta2.setString(consulta.getString("Data").split(" ")[0]);
                        consulta2.setInt(consulta.getInt("Seqrota"));
                        consulta2.setInt(consulta.getInt("Parada"));
                        consulta2.setString(consulta.getString("ER"));
                        consulta2.setString(consulta.getString("CodCli1"));
                        consulta2.setString(consulta.getString("Hora1"));
                        consulta2.setString(consulta.getString("HrCheg"));
                        consulta2.setString(consulta.getString("HrSaida"));
                        consulta2.setInt(consulta.getInt("TempoEspera"));
                        consulta2.setString(consulta.getString("Guia"));
                        consulta2.setString(consulta.getString("Serie"));
                        consulta2.setString("SATMOB");
                        consulta2.setString(DtAlter);
                        consulta2.setString(HrAlter);

                        consulta2.insert();
                    }
                } catch (Exception ex) {
                }
            }

        } catch (Exception e) {
            throw new Exception("CtrOperEquipDao.processarCtrOperEquip - " + e.getMessage() + "\r\n" + sql);
        }
    }
}
