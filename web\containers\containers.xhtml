<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
    <f:view locale="#{localeController.currentLocale}">
        <h:head>
            <link rel="icon" href="../assets/images/favicon.png" />
            <title>#{localemsgs.SatMOB} </title>
            <meta http-equiv="X-UA-Compatible" content="IE=edge" />
            <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no"/>
            <link type="text/css" href="../assets/css/flag-icon.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/bootstrap.css" rel="stylesheet"/>
            <link type="text/css" href="../assets/css/servicos.css" rel="stylesheet"/>
            <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.2/css/all.css" integrity="sha384-oS3vJWv+0UjzBfQzYUhtDYW+Pj2yciDJxpsK1OYPAYjqT085Qq/1cq5FLXAZQ7Ay" crossorigin="anonymous"/>
            <script src="https://maps.google.com/maps/api/js?key=#{login.googleApiOper}" type="text/javascript" ></script>
            <script src="../assets/js/primefaces_locales.js" library="primefaces" type="text/javascript" ></script>
            <script src="../assets/scripts/south-1.0.0.js" type="text/javascript"></script>
            <style>
                .AbrirFiltros{
                    position: absolute;
                    z-index:1 !important;
                    background-color:#000;
                    color:#FFF;
                    writing-mode: vertical-rl;
                    text-orientation: upright !important;
                    padding:10px 6px 10px 6px !important;
                    top:80px;
                    border-radius: 0px 8px 8px 0px;
                    box-shadow: 1px 1px 2px #666;
                    cursor: pointer;
                }
                               
                #divQuadroResumo .ui-selectonemenu-label,
                #divQuadroResumo > .ui-selectonemenu-label{
                    background-color:#CED2DA !important;
                    padding:5px 3px 6px 10px !important;
                    box-shadow:none !important;
                    border:none !important;
                }
                
                #divQuadroResumo{
                    position:absolute !important;
                    width:240px !important;
                    top:70px !important;
                    /*left:26px !important;*/
                    z-index:1 !important;
                    height: 100%;
                    max-height:500px !important;
                }
                
                #divQuadroResumo .FecharFiltros{
                    position:absolute;
                    right:-10px;
                    top:-15px;
                    color:#FFF;
                    background-color:#000;
                    width:35px;
                    height: 35px;
                    border-radius:50%;
                    text-align: center;
                    cursor: pointer;
                    padding-top: 5px;
                    padding-left: 1px;
                    font-size:17pt !important;
                    box-shadow:1px 1px 2px #666;
                }
                
                #divQuadroResumo .ItemResumo{
                    background-color: rgba(211, 221, 228, 0.8) !important;
                    width:100% !important;
                    border-radius:12px;
                    padding:1px 1px 0px 1px !important;
                    box-shadow:1px  1px 3px #666;
                    border-radius:4px;
                }
                
                #divQuadroResumo .FiltrosMapa{
                    width:100%;
                    padding:8px 4px 8px 4px !important;
                    outline:none;
                    border:thin solid #CCC;
                    background-color:#EEE;
                    color:#505050;
                    cursor:pointer
                }
                
                #divQuadroResumo input[type="checkbox"]{
                    padding:0 !important;
                    background-color:black;
                    position:absolute;
                    margin-top:5px !important;
                    border:thin solid #AAA !important;
                    border-radius:2px !important;
                    
                }
                
                #divQuadroResumo input[type="checkbox"] + label{
                    font-size:10pt;
                    font-weight:600;
                    color:#505050;
                    margin:0px !important;
                    padding:0px 0px 0px 16px !important;
                    cursor:pointer;
                    text-shadow:1px 1px rgba(255, 255, 255, 0.5);
                }
                
                #divQuadroResumo .Item,
                #divQuadroResumo .ItemZebrado{
                    position:relative;
                    width:calc(100% - 12px) !important;
                    margin-left: 6px !important;
                    padding:2px 8px 0px 8px !important;
                    border-radius:2px;
                }
                
                #divQuadroResumo .ItemZebrado{
                    background-color:rgba(187, 187, 187, 0.5) !important;
                }
                
                #divQuadroResumo #lblTituloFiltro{
                    font-size:8pt !important;
                    font-weight:600 !important;
                    color:#000 !important;
                    padding:0px 0px 0px 10px !important;
                    text-shadow:1px 1px rgba(255, 255, 255, 0.5);
                    border:none !important;
                }
                
                #divQuadroResumo .QdeStatus{
                    position: absolute;
                    width:45px;
                    height:18px !important;
                    border-radius:4px;
                    right:6px;
                    top: 5px;
                    font-size:8pt !important;
                    text-align:center;
                    color:#FFF;
                    font-weight:bold;
                    padding:0x !important;
                }
                
                #divQuadroResumo .QdeStatus[cor="R"]{
                    background-color:#ff0000;
                    border:thin solid #cc0000;
                }
                
                #divQuadroResumo .QdeStatus[cor="G"]{
                    background-color: #00cc00;
                    border:thin solid #009900;
                }
                
                #divQuadroResumo .QdeStatus[cor="Y"]{
                    background-color: #ffba00;
                    border:thin solid #cca300;
                }
                
                #divQuadroResumo .QdeStatus[cor="W"]{
                    background-color:#FFF;
                    color:#505050;
                }
                
                #divQuadroResumo .fa-fw{
                    float:left;
                    width:100%;
                    text-align:center;
                    margin-top:2px;
                }
                
                div[id*="infoWindowCamcambas"] .panelgrid .ui-widget{
                    width:100% !important;
                    z-index:2 !important;
                }
                
                div[id*="infoWindowCamcambas"] .ui-panelgrid-cell{
                    text-align:left !important;
                    font-size:9pt !important;
                    white-space: nowrap !important;
                    width:100% !important;
                }
                
                div[id*="infoWindowCamcambas"] .Titulo{
                    font-weight:bold;
                    width:100% !important;
                    text-align:left;
                    font-size:12pt !important;
                    color:#5065a1;
                }
                
                div[id*="infoWindowCamcambas"] .Conteudo{
                    font-weight:bold;
                    width:100% !important;
                    text-align:left !important;
                    font-size:10pt !important;
                    color:#000;
                    display:block;
                }
                
                .tblDadosWindowInfo{
                    width:100% !important;
                    padding: 0px !important;
                    box-shadow: 2px 2px 3px #CCC;
                    border:thin solid #DDD;
                    border-spacing: 2px !important;
                    border-collapse:separate;
                }
                
                .tblDadosWindowInfo thead tr th{
                    background-color:#5065a1;
                    border: thin solid #445a9a;
                    color:#FFF!important;
                    text-align:center;
                    font-weight:bold;
                    padding:6px !important;
                }
                
                .tblDadosWindowInfo tbody tr td{
                    background-color:#FFF;
                    color:#666 !important;
                    text-align:center;
                    border:thin solid #DDD;
                    font-weight:500;
                    padding:4px 6px 4px 6px !important;
                }
                
                .TotalGeral{
                    background-color: #000;
                    color:#FFF !important;
                    position:relative !important;
                    display:block !important;
                    margin-top:-1px;
                    border-radius: 3px;
                    width: calc(100% - 12px) !important;
                    margin-left:6px;
                    padding:4px 6px 4px 10px !important;
                    font-weight: bold !important;
                    font-size:10pt;
                    text-transform: uppercase !important;
                }
                
                .TotalGeral label{
                    position: absolute;
                    width:45px;
                    height:18px;
                    border-radius:4px;
                    right:6px;
                    top: 4px;
                    font-size:8pt !important;
                    text-align:center;
                    color:#FFF;
                    font-weight:bold;
                    padding-top:0x !important;
                    background-color:#666;
                    color:#FFF;
                    border:thin solid #999 !important;
                    font-weight: bold !important;
                }
            </style>
        </h:head>
        <h:body>

            <f:metadata>
                <f:viewAction action="#{valores.Persistencia(login.pp, login.satellite)}"/>
                <f:viewAction action="#{valores.mapaContainers2()}"/>
            </f:metadata>

            <p:growl id="msgs"/>

            <ui:composition template = "../assets/template/page.xhtml">	

                <ui:define name="menu">
                    <ui:include src="../assets/template/menu.xhtml" />
                </ui:define>

                <ui:define name="top-menu">
                    <ui:include src="../assets/template/header.xhtml" />  
                </ui:define>            
                
                <ui:define name="infoFilial">
                    <div class="ui-grid-row">
                        <h:outputText value="#{valores.filiais.descricao}" class="negrito"/>
                    </div>
                    <div class="ui-grid-row">
                        #{valores.filiais.endereco}
                    </div>
                    <div class="ui-grid-row">
                        #{valores.filiais.bairro}<h:outputText value=", " rendered="#{valores.filiais.bairro ne null and valores.filiais.bairro ne ''}"/>#{valores.filiais.cidade}/#{valores.filiais.UF}
                    </div>
                </ui:define>                

                <ui:define name="body">
                    <div class="box box-primary" style="font-family: 'Helvetica Neue';font-size: 15px">
                        <h:form id="formMaps">
                            <!--<p:hotkey bind="esc" oncomplete="PF('dlgMaps').hide()"/>-->
                            <p:panel styleClass="painelCadastro" id="cadastrar">
                                <label class="AbrirFiltros" style="left:0px; display: none;"><i class="fa fa-filter"></i>&nbsp;<h:outputText value="#{localemsgs.Filtrar.toUpperCase()}" /></label>
                                <div id="divQuadroResumo" style="left: 23px;">
                                <h:panelGroup id="panelQuadroResumo">
                                    <h:outputText class="fa fa-times FecharFiltros" title="#{localemsgs.Fechar}" style="display:block" />
                                    
                                    <div class="ItemResumo" style="padding-bottom: 1px !important;">
                                        <label id="lblTituloFiltro">
                                            <i class="fa fa-filter"/>
                                            &nbsp;
                                            <h:outputText value="#{localemsgs.PrazoColeta.toUpperCase()}" />
                                        </label>
                                        <div class="ItemZebrado">
                                            <label class="QdeStatus" cor="R">#{valores.contadorVermelho}</label>
                                            <label>
                                                <p:selectBooleanCheckbox id="chkColetaVencida" value="#{valores.filtroColetaVencida}">
                                                    <p:ajax listener="#{valores.aplicarFiltros}" update="gMapCacambas panelQuadroResumo" />
                                                </p:selectBooleanCheckbox>
                                                &nbsp;
                                                <label for='chkColetaVencida'>
                                                    <h:outputText value=" #{localemsgs.ColetasVencidas}" />
                                                </label>
                                            </label>
                                        </div>
                                        <div class="Item">
                                            <label class="QdeStatus" cor="Y">#{valores.contadorAmarelo}</label>
                                            <label>
                                                <p:selectBooleanCheckbox id="chkColetaVencendo" value="#{valores.filtroColetaVencendo}">
                                                    <p:ajax listener="#{valores.aplicarFiltros}" update="gMapCacambas panelQuadroResumo" />
                                                </p:selectBooleanCheckbox>
                                                &nbsp;
                                                <label for='chkColetaVencendo'>
                                                    <h:outputText value="#{localemsgs.ColetasHoje}"/>
                                                </label>
                                            </label>
                                        </div>
                                        <div class="ItemZebrado">
                                            <label class="QdeStatus" cor="G" style="padding:1px !important">#{valores.contadorVerde}</label>
                                            <label>
                                                <p:selectBooleanCheckbox id="chkColetaVencer" value="#{valores.filtroColetaVencer}">
                                                    <p:ajax listener="#{valores.aplicarFiltros}" update="gMapCacambas panelQuadroResumo" />
                                                </p:selectBooleanCheckbox>
                                                &nbsp;
                                                <label for='chkColetaVencer'>
                                                    <h:outputText value="#{localemsgs.ColetasVencer}" />
                                                </label>

                                            </label>
                                        </div>
                                        <div class="Item">
                                            <label class="QdeStatus" cor="W" style="padding:1px !important">#{valores.contadorBranco}</label>
                                            <label>
                                                <p:selectBooleanCheckbox id="chkColetaSemPrazo" value="#{valores.filtroColetaSemPrazo}">
                                                    <p:ajax listener="#{valores.aplicarFiltros}" update="gMapCacambas panelQuadroResumo" />
                                                </p:selectBooleanCheckbox>
                                                &nbsp;
                                                <label for='chkColetaSemPrazo'>
                                                    <h:outputText value="#{localemsgs.SemPrazoColeta}" />
                                                </label>
                                            </label>
                                        </div>
                                        <label class="TotalGeral">
                                            <h:outputText value="#{localemsgs.Total}" />
                                            <label ref="Total">#{valores.contadorTotal}</label>
                                        </label>
                                    </div>

                                    <div class="ItemResumo" style="margin-top:10px; padding-bottom: 7px !important;">
                                        <label id="lblTituloFiltro">
                                            <i class="fa fa-filter"/>
                                            &nbsp;
                                            <h:outputText value="TIPO EQUIPAMENTO"/>
                                        </label>
                                        <div class="ItemZebrado" style="padding:1px !important;">
                                            <p:selectOneMenu id="cboFiltroTipoEquipamento" value="#{valores.filtroTipoEquipamento}" style="width:100%;border:none; outline:none; padding:0px !important;">
                                                <f:selectItem itemLabel="TODOS" itemValue="TODOS" />
                                                <f:selectItem itemLabel="Caçamba Orgânica" itemValue="CO" />
                                                <f:selectItem itemLabel="Container Rodinha" itemValue="CR" />
                                                <f:selectItem itemLabel="Container Almoxarifado" itemValue="CA" />
                                                <f:selectItem itemLabel="Lixeira" itemValue="LX" />
                                                <f:selectItem itemLabel="Totem Remédio" itemValue="TR" />
                                                <f:selectItem itemLabel="Tipo CP" itemValue="CP" />
                                                <p:ajax listener="#{valores.aplicarFiltros}" update="gMapCacambas panelQuadroResumo" />
                                            </p:selectOneMenu>
                                        </div>
                                    </div>

                                    <div class="ItemResumo" style="margin-top:10px; padding-bottom: 7px !important;">
                                        <label id="lblTituloFiltro">
                                            <i class="fa fa-filter"/>
                                            &nbsp;
                                            <h:outputText value="#{localemsgs.Bairro.toUpperCase()}"/>
                                        </label>
                                        <div class="ItemZebrado" style="padding:1px !important;">
                                            <p:selectOneMenu id="cboFiltroBairros" value="#{valores.filtroBairro}" style="width:100%;border:none; outline:none; padding:0px !important;">
                                                <f:selectItem itemLabel="[ #{localemsgs.Todos.toUpperCase()} ]" itemValue="TODOS" />
                                                <f:selectItems value="#{valores.listaBairros}" var="bairro" itemLabel="#{bairro}" itemValue="#{bairro}" />
                                                <p:ajax listener="#{valores.aplicarFiltros}" update="gMapCacambas panelQuadroResumo" />
                                            </p:selectOneMenu>
                                        </div>
                                    </div>

                                    <div class="ItemResumo" style="margin-top:10px; padding-bottom: 7px !important;">
                                        <label id="lblTituloFiltro">
                                            <i class="fa fa-filter"/>
                                            &nbsp;
                                            <h:outputText value="#{localemsgs.Cliente.toUpperCase()}"/>
                                        </label>
                                        <div class="ItemZebrado" style="padding:1px !important;">
                                            <p:selectOneMenu id="cboFiltroCliente" value="#{valores.filtroCliente}" style="width:100%;border:none; outline:none; padding:0px !important;">
                                                <f:selectItem itemLabel="[ #{localemsgs.Todos.toUpperCase()} ]" itemValue="TODOS" />
                                                <f:selectItems value="#{valores.listaClientesFiltro}" var="cliente" itemLabel="#{cliente}" itemValue="#{cliente}" />
                                                <p:ajax listener="#{valores.aplicarFiltros}" update="gMapCacambas panelQuadroResumo" />
                                            </p:selectOneMenu>
                                        </div>
                                        <div class="ItemZebrado" style="padding:1px !important; margin-top: 3px">
                                            <p:selectOneMenu id="cboFiltroServicoCliente" value="#{valores.filtroClienteServico}" style="width:100%;border:none; outline:none; padding:0px !important;">
                                                <f:selectItem itemLabel="[ #{localemsgs.Todos.toUpperCase()} ]" itemValue="TODOS" />
                                                <f:selectItems value="#{valores.listaClientesServico}" var="clienteServ" itemLabel="#{clienteServ}" itemValue="#{clienteServ}" />
                                                <p:ajax listener="#{valores.aplicarFiltros}" update="gMapCacambas panelQuadroResumo" />
                                            </p:selectOneMenu>
                                        </div>
                                    </div>

                                    <div class="ItemResumo" style="margin-top:10px; padding-bottom: 7px !important;">
                                        <label id="lblTituloFiltro">
                                            <i class="fa fa-filter"/>
                                            &nbsp;
                                            <h:outputText value="#{localemsgs.Solicitante.toUpperCase()}"/>
                                        </label>
                                        <div class="ItemZebrado" style="padding:1px !important;">
                                            <p:selectOneMenu id="cboFiltroSolicitante" value="#{valores.filtroSolicitante}" style="width:100%;border:none; outline:none; padding:0px !important;">
                                                <f:selectItem itemLabel="[ #{localemsgs.Todos.toUpperCase()} ]" itemValue="TODOS" />
                                                <f:selectItems value="#{valores.listaSolicitantes}" var="solicitante" itemLabel="#{solicitante}" itemValue="#{solicitante}" />
                                                <p:ajax listener="#{valores.aplicarFiltros}" update="gMapCacambas panelQuadroResumo" />
                                            </p:selectOneMenu>
                                        </div>
                                    </div>
                                    
                                    <div class="ItemResumo" style="margin-top:10px; padding-bottom: 7px !important;">
                                        <label id="lblTituloFiltro">
                                            <i class="fa fa-filter"/>
                                            &nbsp;
                                            <h:outputText value="#{localemsgs.TempoCliente.toUpperCase()}"/>
                                        </label>
                                        <div class="ItemZebrado" style="padding:1px !important;">
                                            <p:selectOneMenu id="cboFiltroTempo" value="#{valores.filtroTempo}" style="width:100%;border:none; outline:none; padding:0px !important;">
                                                <f:selectItem itemLabel="[ #{localemsgs.Todos.toUpperCase()} ]" itemValue="TODOS" />
                                                <f:selectItem itemLabel="#{localemsgs.Ate.toUpperCase()} 5 #{localemsgs.Dias.toUpperCase()}" itemValue="5" />
                                                <f:selectItem itemLabel="#{localemsgs.Ate.toUpperCase()} 7 #{localemsgs.Dias.toUpperCase()}" itemValue="7" />
                                                <f:selectItem itemLabel="#{localemsgs.Ate.toUpperCase()} 15 #{localemsgs.Dias.toUpperCase()}" itemValue="15" />
                                                <f:selectItem itemLabel="#{localemsgs.Mais15Dias.toUpperCase()}" itemValue="9999" />
                                                <p:ajax listener="#{valores.aplicarFiltros}" update="gMapCacambas panelQuadroResumo" />
                                            </p:selectOneMenu>
                                        </div>
                                    </div>

                                    <!-- Botão para verificar containers sem posicionamento -->
                                    <div class="ItemResumo" style="margin-top:15px; padding-bottom: 7px !important;">
                                        <p:commandButton id="btnContainersSemPosicionamento"
                                                         value="Containers Sem Posição"
                                                         action="#{valores.verificarContainersSemPosicionamento}"
                                                         update="gridContainersSemPosicionamento"
                                                         styleClass="btn btn-warning"
                                                         icon="fa fa-map-marker"
                                                         style="width: 100%; font-size: 11px; padding: 8px 5px;"
                                                         title="Verificar containers que não possuem coordenadas cadastradas" />
                                    </div>
                                </h:panelGroup>
                                </div>
                                
                                <p:gmap widgetVar="gMapCacambas" id="gMapCacambas" center="#{valores.centroMapa}" zoom="10" type="ROADMAP" 
                                        style="height:85vh" model="#{valores.posicaoRotas}" fitBounds="false" disableDefaultUI="false" styleClass="map">

                                    <p:ajax event="overlaySelect" listener="#{valores.selecionarPinoConteiner}" />

                                    <p:gmapInfoWindow id="infoWindowCamcambas" >
                                        <p:panelGrid columns="1" style="text-align: center; display: block; margin: auto; border:none !important;" >
                                            <h:outputText class="Titulo" value="#{valores.posicaoContainer.IDEquip} - #{valores.posicaoContainer.nred}" style="font-weight: bold; border:none !important;"/>
                                            <label class="Conteudo"><i class="fa fa-map-marker"></i>&nbsp;&nbsp;#{valores.posicaoContainer.ende}</label>
                                            <label class="Conteudo" style="margin-left:15px !important; width:calc(100% - 15px) !important; font-size:7pt !important; color:#505050 !important;">#{valores.posicaoContainer.bairro} - #{valores.posicaoContainer.cidade}/#{valores.posicaoContainer.uf}</label>
                                            <table class="tblDadosWindowInfo">
                                                <thead>
                                                    <tr>
                                                        <th>#{localemsgs.DataHora}</th>
                                                        <th>#{localemsgs.TempoDias}</th>
                                                        <th>Data Prevista</th>
                                                        <th>#{localemsgs.Motorista}</th>
                                                        <th>#{localemsgs.QtdCacambas}</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>#{valores.posicaoContainer.dataEntrega} #{valores.posicaoContainer.hrcheg}</td>
                                                        <td>#{valores.posicaoContainer.tempoDias}</td>
                                                        <td>#{valores.posicaoContainer.dataPrevistaColeta}</td>
                                                        <td>#{valores.posicaoContainer.motorista}</td>
                                                        <td>#{valores.posicaoContainer.qtdeCacambas}</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </p:panelGrid>
                                    </p:gmapInfoWindow>
                                </p:gmap>
                            </p:panel>

                            <!-- Grid/Dialog para exibir containers sem posicionamento -->
                            <p:dialog id="gridContainersSemPosicionamento"
                                      header="Containers Sem Posicionamento"
                                      widgetVar="dlgContainersSemPosicionamento"
                                      visible="#{valores.exibirGridSemPosicionamento}"
                                      modal="true"
                                      resizable="true"
                                      width="95%"
                                      height="80%"
                                      responsive="true"
                                      closeOnEscape="true">

                                <p:panel style="margin-bottom: 10px; background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 10px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                                        <h:outputText value="Total de containers sem posicionamento: #{valores.containersSemPosicionamento.size()}"
                                                     style="font-weight: bold; color: #d9534f; font-size: 14px;" />
                                        <p:commandButton value="Fechar"
                                                       action="#{valores.fecharGridSemPosicionamento}"
                                                       update="gridContainersSemPosicionamento"
                                                       styleClass="btn btn-secondary"
                                                       icon="fa fa-times"
                                                       style="margin-left: 10px;" />
                                    </div>
                                </p:panel>

                                <p:dataTable id="tabelaContainersSemPosicionamento"
                                             value="#{valores.containersSemPosicionamento}"
                                             var="containerSemPos"
                                             paginator="true"
                                             rows="20"
                                             paginatorPosition="both"
                                             paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                                             rowsPerPageTemplate="10,20,50,100"
                                             emptyMessage="Nenhum container sem posicionamento encontrado."
                                             styleClass="table table-striped table-bordered"
                                             style="margin-top: 10px; font-size: 12px;">

                                    <f:facet name="header">
                                        <span style="font-weight: bold;">Lista de Containers Sem Coordenadas</span>
                                    </f:facet>

                                    <p:column headerText="ID Equipamento" sortBy="#{containerSemPos.IDEquip}" style="width: 120px;">
                                        <h:outputText value="#{containerSemPos.IDEquip}" style="font-weight: bold;" />
                                    </p:column>

                                    <p:column headerText="Tipo" sortBy="#{containerSemPos.tipoEquip}" style="width: 60px;">
                                        <span class="label label-info" style="padding: 3px 6px; border-radius: 3px; font-size: 10px;">
                                            #{containerSemPos.tipoEquip}
                                        </span>
                                    </p:column>

                                    <p:column headerText="Cliente Serviço" sortBy="#{containerSemPos.nred}">
                                        <h:outputText value="#{containerSemPos.nred}" />
                                    </p:column>

                                    <p:column headerText="Cliente Faturamento" sortBy="#{containerSemPos.nredFat}">
                                        <h:outputText value="#{containerSemPos.nredFat}" />
                                    </p:column>

                                    <p:column headerText="Bairro" sortBy="#{containerSemPos.bairro}">
                                        <h:outputText value="#{containerSemPos.bairro}" />
                                    </p:column>

                                    <p:column headerText="Data Última Mov." sortBy="#{containerSemPos.dtUltMov}" style="width: 120px;">
                                        <h:outputText value="#{containerSemPos.dtUltMov}" />
                                    </p:column>

                                    <p:column headerText="Tempo (Dias)" sortBy="#{containerSemPos.tempoDias}" style="width: 80px; text-align: center;">
                                        <h:outputText value="#{containerSemPos.tempoDias}" style="font-weight: bold;" />
                                    </p:column>

                                    <p:column headerText="Latitude" style="width: 80px; text-align: center;">
                                        <h:outputText value="#{empty containerSemPos.latitude or containerSemPos.latitude eq '' ? 'VAZIO' : containerSemPos.latitude}"
                                                     style="color: #{empty containerSemPos.latitude or containerSemPos.latitude eq '' ? '#d9534f' : '#5cb85c'}; font-weight: bold; font-size: 11px;" />
                                    </p:column>

                                    <p:column headerText="Longitude" style="width: 80px; text-align: center;">
                                        <h:outputText value="#{empty containerSemPos.longitude or containerSemPos.longitude eq '' ? 'VAZIO' : containerSemPos.longitude}"
                                                     style="color: #{empty containerSemPos.longitude or containerSemPos.longitude eq '' ? '#d9534f' : '#5cb85c'}; font-weight: bold; font-size: 11px;" />
                                    </p:column>

                                    <p:column headerText="Status" style="width: 100px; text-align: center;">
                                        <span class="label label-danger" style="padding: 4px 8px; border-radius: 3px; font-size: 10px;">
                                            Sem Posição
                                        </span>
                                    </p:column>

                                </p:dataTable>

                                <p:panel style="margin-top: 15px; padding: 10px; background-color: #f0f8ff; border: 1px solid #b0d4f1; border-radius: 4px;">
                                    <h:outputText value="💡 " style="font-size: 16px;" />
                                    <h:outputText value="Dica: Estes containers não aparecem no mapa porque não possuem coordenadas (latitude/longitude) cadastradas. "
                                                 style="font-style: italic; color: #31708f; font-size: 12px;" />
                                    <br/>
                                    <h:outputText value="Entre em contato com o setor responsável para atualizar as coordenadas destes equipamentos."
                                                 style="font-style: italic; color: #31708f; font-size: 12px;" />
                                </p:panel>

                            </p:dialog>
                        </h:form>
                    </div>
            <script type="text/javascript">
                // <![CDATA[
                $(document).ready(function(){
                    // Painel já fica aberto por padrão, não precisa do botão
                    $('.AbrirFiltros').css('display', 'none');
                })
                .on('click','.FecharFiltros', function(){
                    $('#divQuadroResumo').stop().animate({
                        'left': '-260px'
                    }, 700);

                    setTimeout(function(){
                        $('.AbrirFiltros').css('display','').stop().animate({
                            'left': '0px'
                        }, 300);
                        $('.FecharFiltros').fadeOut();
                        $('#divQuadroResumo').css('display','none');
                    }, 700);
                })
                .on('click','.AbrirFiltros', function(){
                    $(this).stop().animate({
                        'left': '-50px'
                    }, 300);

                    setTimeout(function(){
                        $('#divQuadroResumo').css('display','').stop().animate({
                            'left': '23px'
                        }, 700);

                        setTimeout(function(){
                            $('.FecharFiltros').fadeIn(1000);
                        }, 800);

                        $('.AbrirFiltros').css('display','none');
                    }, 300);
                })
                ;
                // Filtros e contadores agora são processados no backend
                // ]]>
            </script>  
                </ui:define>
            </ui:composition>
            
            
            <footer>
                <div class="footer-toggler">
                    <a href="#footer-toggle" id="footer-toggle" >
                        <i class="fa fa-bars" style="font-size: 18px"></i>
                    </a>
                </div>
                <div class="footer-body" id="footer-body">
                    <div>
                        <h:form id="corporativo">
                            <h:outputText value="#{localemsgs.Corporativo}: " />
                            <p:selectBooleanCheckbox value="#{valores.mostrarFiliais}">
                                <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{valores.MostrarFiliais}" />
                            </p:selectBooleanCheckbox>

                            <p:spacer width="20px"/>

                            <h:outputText value="#{localemsgs.SomenteAtivos}: " />
                            <p:selectBooleanCheckbox value="#{valores.excl}">
                                <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{valores.SomenteAtivos}" />
                            </p:selectBooleanCheckbox>

                            <p:spacer width="20px"/>

                            <h:outputText value="#{localemsgs.LimparFiltros}: " />
                            <p:selectBooleanCheckbox value="#{valores.limparFiltros}">
                                <p:ajax update="msgs main:tabela cabecalho corporativo" listener="#{valores.LimparFiltros}" />
                            </p:selectBooleanCheckbox>
                        </h:form>
                    </div>
                    <div class="container">
                        <div class="col-sm-3">
                            <table class="footer-time">
                                <tr>
                                    <td>
                                        <p:clock pattern="HH:mm:ss" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h:outputText value="#{localeController.mostraData}" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-6">
                            <table class="footer-user">
                                <tr>
                                    <td>#{localemsgs.Usuario}: #{login.usuario.nome}</td>
                                    <td><img src="#{login.getLogo(login.empresa.bancoDados)}" height="47px" width="59px"/></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-sm-3">
                            <table class="footer-logos">
                                <tr>
                                    <td><img src="../assets/img/logo_satweb.png" /></td>
                                    <td>
                                        <h:form>
                                            <h:commandLink actionListener="#{localeController.increment}" 
                                                           action="#{localeController.getLocales}" >
                                                <p:graphicImage url="../assets/img/#{localeController.number}.png" height="25" />
                                            </h:commandLink>
                                        </h:form>   
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </footer>
                        
        </h:body>
    </f:view>
</html>
