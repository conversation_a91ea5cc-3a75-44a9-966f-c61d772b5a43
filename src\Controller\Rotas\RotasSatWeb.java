package Controller.Rotas;

import Dados.Persistencia;
import Dados.Pool.SasPoolPersistencia;
import SasBeans.Clientes;
import SasBeans.ClientesGuia;
import SasBeans.CxFGuiasVol;
import SasBeans.CxForte;
import SasBeans.EmailsEnviar;
import SasBeans.EmailsEnviarAnexo;
import SasBeans.Escala;
import SasBeans.Filiais;
import SasBeans.Funcion;
import SasBeans.Municipios;
import SasBeans.OS_Vig;
import SasBeans.Paramet;
import SasBeans.Pedido;
import SasBeans.Pessoa;
import SasBeans.RamosAtiv;
import SasBeans.Rastrear;
import SasBeans.Regiao;
import SasBeans.Rotas;
import SasBeans.Rt_Escala;
import SasBeans.Rt_Guias;
import SasBeans.Rt_Hist;
import SasBeans.Rt_Modelo;
import SasBeans.Rt_Perc;
import SasBeans.Veiculos;
import SasBeansCompostas.EGtv;
import SasBeansCompostas.PreOrderManifesto;
import SasDaos.ClientesDao;
import SasDaos.CxFGuiasDao;
import SasDaos.CxFGuiasVolDao;
import SasDaos.CxForteDao;
import SasDaos.EGtvDao;
import SasDaos.EmailsEnviarAnexoDao;
import SasDaos.EmailsEnviarDao;
import SasDaos.EscalaDao;
import SasDaos.FiliaisDao;
import SasDaos.FuncionDao;
import SasDaos.LoginDao;
import SasDaos.MunicipiosDao;
import SasDaos.OS_VigDao;
import SasDaos.ParametDao;
import SasDaos.PedidoDao;
import SasDaos.PessoaDao;
import SasDaos.PreOrderDao;
import SasDaos.RPVDao;
import SasDaos.RamosAtivDao;
import SasDaos.RastrearDao;
import SasDaos.RegiaoDao;
import SasDaos.RotasDao;
import SasDaos.Rt_EscalaDao;
import SasDaos.Rt_GuiasDao;
import SasDaos.Rt_HistDao;
import SasDaos.Rt_ModeloDao;
import SasDaos.Rt_PercDao;
import SasDaos.VeiculosDao;
import br.com.sasw.pacotesuteis.sasbeans.CtrOperEquip;
import br.com.sasw.pacotesuteis.sasbeans.formatadas.MovimentacaoContainer;
import br.com.sasw.pacotesuteis.sasdaos.CtrOperEquipDao;
import static br.com.sasw.pacotesuteis.utilidades.DataAtual.getDataAtual;
import br.com.sasw.pacotesuteis.utilidades.EnvioEmail;
import br.com.sasw.pacotesuteis.utilidades.FuncoesString;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaAteEspaço;
import static br.com.sasw.pacotesuteis.utilidades.FuncoesString.RecortaString;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.w3c.dom.Document;
import org.w3c.tidy.Tidy;
import org.xhtmlrenderer.pdf.ITextRenderer;

/**
 * <AUTHOR> Maracaipe
 */
public class RotasSatWeb {
    
    private Boolean isTranspCacamba(String empresa) throws Exception {
        SasPoolPersistencia pool = new SasPoolPersistencia();
        pool.setCaminho("/Dados/mapconect_deploy.txt");
        Persistencia inSatellite;
        inSatellite = pool.getConexao("SATELLITE", "");

        ParametDao parametDao = new ParametDao();
        Paramet parametGoogle = parametDao.getParametGoogleApi(empresa, inSatellite);

        return parametGoogle.getTranspCacamba().equals("0") ? false : true;
    }

    public List<Rotas> relatorioResumoRotas(String codFil, String data, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            return rotasDao.resumoRotas(codFil, data, data, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Rotas> relatorioResumoRotas(String codFil, String dataInicial, String dataFinal, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            return rotasDao.resumoRotas(codFil, dataInicial, dataFinal, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Clientes inserirCliente(Clientes clientes, BigDecimal codPessoa, String orcamento, Persistencia persistencia) throws Exception {
        try {
            int contador = 1;
            Clientes cliente = clientes;
            ClientesDao clientesdao = new ClientesDao();
            OS_Vig osvig;
            OS_VigDao osvigdao = new OS_VigDao();
            String codcli;

            while (contador <= 30) {
                try {
                    codcli = clientesdao.getCodCliBancoTpCli(cliente.getCodFil(), "777", cliente.getTpCli(), persistencia);
                    osvig = osvigdao.dadosFatCacambas(codPessoa.toString().replace(".0", ""), persistencia);

                    cliente.setBanco(codcli.substring(0, 3));
                    cliente.setCodCli(codcli.substring(4, 7));
                    cliente.setCodigo(codcli);
                    cliente.setAgencia(codcli.substring(3, 7));
                    cliente.setNRed(RecortaString((RecortaAteEspaço(osvig.getNRedFat(), 0, 10) + " " + cliente.getNRed()), 0, 20));
                    cliente = (Clientes) FuncoesString.removeAcentoObjeto(cliente);

                    clientesdao.inserir(cliente, persistencia);

                    OS_VigDao.inserirOsReduzida(
                            cliente.getCodFil().toString(),
                            cliente.getNRed(),
                            "1",
                            "001",
                            cliente.getCodigo(), //Origem
                            cliente.getNRed(), //Origem
                            osvig.getCliDst(), //Destino
                            osvig.getNRedDst(), //Destino
                            osvig.getCliFat(), //Faturar
                            osvig.getNRedFat(), //Faturar
                            osvig.getViaCxF(),
                            osvig.getContrato(), //Contrato
                            osvig.getAgrupador().toString(), //Agrupador
                            osvig.getOSGrp().toString(),
                            "A",
                            cliente.getOper_Inc(),
                            cliente.getDt_Alter().toString(),
                            cliente.getHr_Alter(),
                            cliente.getOper_Alt(),
                            cliente.getDt_Alter().toString(),
                            cliente.getHr_Alter(),
                            osvig.getSitFiscal(),
                            "",
                            "999",
                            "0001",
                            orcamento,
                            persistencia);

                    return cliente;

                } catch (Exception ex) {
                } finally {
                    contador++;
                }
            }

            throw new Exception("RotasSatWeb.concorrencia");
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista os municípios com base na query
     *
     * <AUTHOR>
     * @param query - string com a busca
     * @param persistencia - conexão ao banco de dados
     * @return - lista de municipios
     * @throws Exception
     */
    public List<Municipios> listarMunicipios(String query, Persistencia persistencia) throws Exception {
        try {
            MunicipiosDao municipiosdao = new MunicipiosDao();
            return municipiosdao.BuscarMunicipio(query, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhagearl<message>" + e.getMessage());
        }
    }

    /**
     * Lista os municípios com base no estado e cidade
     *
     * <AUTHOR>
     * @param estado
     * @param cidade
     * @param persistencia - conexão ao banco de dados
     * @return - lista de municipios
     * @throws Exception
     */
    public List<Municipios> listarMunicipios(String estado, String cidade, Persistencia persistencia) throws Exception {
        try {
            MunicipiosDao municipiosdao = new MunicipiosDao();
            return municipiosdao.ValidarMunicipio(estado, cidade, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhagearl<message>" + e.getMessage());
        }
    }

    public List<RamosAtiv> listarRamosAtiv(Persistencia persistencia) throws Exception {
        try {
            RamosAtivDao ramosAtivDao = new RamosAtivDao();
            return ramosAtivDao.listarRamosAtiv(persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Regiao> listarRegioes(String codfil, Persistencia persistencia) throws Exception {
        try {
            RegiaoDao regiaoDao = new RegiaoDao();
            return regiaoDao.listarRegioes(codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public boolean inserirRamosAtiv(RamosAtiv ramosAtiv, Persistencia persistencia) throws Exception {
        try {
            RamosAtivDao ramosAtivDao = new RamosAtivDao();
            if (ramosAtivDao.existeRamoAtiv(ramosAtiv.getCodigo(), persistencia)) {
                return false;
            }
            return ramosAtivDao.inserirRamosAtiv(ramosAtiv, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public boolean inserirRegiao(Regiao regiao, Persistencia persistencia) throws Exception {
        try {
            RegiaoDao regiaoDao = new RegiaoDao();
            if (regiaoDao.existeRegiao(regiao.getRegiao(), regiao.getCodFil(), persistencia)) {
                return false;
            }
            return regiaoDao.inserirRegiao(regiao, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public String inserirPedido(Pedido pedido, Persistencia persistencia) throws Exception {
        try {
            PedidoDao pedidoDao = new PedidoDao();
            String retorno = pedidoDao.salvarPedido(pedido, persistencia);
            if ((pedido.getCodCli1().equals("9996900")) && (isTranspCacamba(persistencia.getEmpresa()))) {
                OS_VigDao osvigdao = new OS_VigDao();
                osvigdao.atualizarGtvQtde(pedido.getOS().toString(), pedido.getCodFil().toString(), persistencia);
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void editarPedido(Pedido pedido, Persistencia persistencia) throws Exception {
        try {
            PedidoDao pedidoDao = new PedidoDao();
            pedidoDao.editarPedido(pedido, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<OS_Vig> buscarOS(String query, String codigo, boolean coleta, Persistencia persistencia) throws Exception {
        try {
            OS_VigDao os_vigDao = new OS_VigDao();
            return os_vigDao.listarPedidoOSConatiner(query, codigo, coleta, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public OS_Vig buscarOSContainer(String cliente, String data, Persistencia persistencia) throws Exception {
        try {
            OS_VigDao os_vigDao = new OS_VigDao();
            return os_vigDao.obterOSContainer(cliente, data, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<CxFGuiasVol> listarLacres(String guia, String serie, Persistencia persistencia) throws Exception {
        try {
            CxFGuiasVolDao cxfguiasvoldao = new CxFGuiasVolDao();
            return cxfguiasvoldao.getLacres(guia, serie, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Rt_Guias obterInfoGuia(EGtv eGTV, Persistencia persistencia) throws Exception {
        try {
            Rt_GuiasDao rt_guiasDao = new Rt_GuiasDao();
//            if(eGTV.getOperacao().equals("E") || eGTV.getOperacao().contains("Entrega") || eGTV.getOperacao().contains("Alívio"))
//                return rt_guiasDao.infoGuiaEntrega(eGTV.getGuia(), eGTV.getSerie(), persistencia);
//            else
            return rt_guiasDao.infoGuia(eGTV.getGuia(), eGTV.getSerie(), persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<MovimentacaoContainer> listarMovimentacaoContainer(String container, String codFil,
            String dtInicio, String dtFim, Persistencia persistencia) throws Exception {
        try {
            CxFGuiasVolDao cxfguiasvolDao = new CxFGuiasVolDao();
            return cxfguiasvolDao.listarMovimentacaoContainer(container, codFil, dtInicio, dtFim, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<PreOrderManifesto> listarManifestosPreOrder(String sequencia, int parada, Persistencia persistencia) throws Exception {
        try {
            PreOrderDao preOrderDao = new PreOrderDao();
            return preOrderDao.gerarManifesto(sequencia, String.valueOf(parada), persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public String gerarCabecalho(String sequencia, int parada, Persistencia persistencia) throws Exception {
        try {
            RPVDao rpvDao = new RPVDao();
            return rpvDao.gerarCabecalho(sequencia, String.valueOf(parada), persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Filiais buscaInfoFilial(String CodFil, Persistencia persistencia) throws Exception {
        try {
            FiliaisDao filialdao = new FiliaisDao();
            return filialdao.getFilial(CodFil, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Rt_Perc> listarParadasPreOrder(String sequencia, Persistencia persistencia) throws Exception {
        try {
            Rt_PercDao rt_PercDao = new Rt_PercDao();
            return rt_PercDao.listarParadasPreOrder(sequencia, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Rt_Modelo> listarModelosRota(String codfil, Boolean excl, Persistencia persistencia) throws Exception {
        try {
            Rt_ModeloDao rt_modeloDao = new Rt_ModeloDao();
            return rt_modeloDao.listarModelosRotaResumido(codfil, excl, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<CxFGuiasVol> listarContainers(String codFil, Persistencia persistencia) throws Exception {
        try {
            CxFGuiasVolDao cxfguiasvolDao = new CxFGuiasVolDao();
            return cxfguiasvolDao.listarContainers(codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Clientes obterClienteTrajeto(String codfil, String codigo, Persistencia persistencia) throws Exception {
        try {
            ClientesDao clientesdao = new ClientesDao();
            return clientesdao.ListaClienteCodigo(codfil, codigo, persistencia).get(0);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Verifica a existência de uma entrada em Rt_Perc para determinada hora e
     * sequencia. Retorna null caso não exista.
     *
     * @param sequencia
     * @param hora1
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Rt_Perc validarHorario(String sequencia, String hora1, Persistencia persistencia) throws Exception {
        try {
            Rt_PercDao rt_PercDao = new Rt_PercDao();
            return rt_PercDao.servicoHora1(sequencia, hora1, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca clientes pra cadastro nos trajetos.
     *
     * @param CodFil
     * @param query
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Clientes> listarClientes(String CodFil, String query, Persistencia persistencia)
            throws Exception {
        try {
            ClientesDao clientesdao = new ClientesDao();
            return clientesdao.ListagemSimplesCliente(CodFil, query, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Clientes> clientesFaturar(String codfil, String sequencia, int p, Persistencia persistencia) throws Exception {
        try {
            List<Clientes> retorno = new ArrayList<>();
            String parada = String.valueOf(p);
            Rt_GuiasDao rt_GuiasDao = new Rt_GuiasDao();
            CxFGuiasDao cxfguiasDao = new CxFGuiasDao();
            Rt_PercDao rt_PercDao = new Rt_PercDao();
            ClientesDao clientesDao = new ClientesDao();

            Boolean pedido = rt_PercDao.existePedido(sequencia, parada, persistencia);
            String ER = rt_PercDao.ER(sequencia, parada, persistencia);
            List<Rt_Guias> guias;
            if (ER.equals("E")) {
                guias = rt_GuiasDao.listaGuiasEntrega(sequencia, parada, pedido, persistencia);
                guias.addAll(cxfguiasDao.listaGuiasEntrega(sequencia, parada, persistencia));
            } else {
                guias = rt_GuiasDao.listaGuias(sequencia, parada, pedido, persistencia);
            }

            String clifat = guias.get(0).getCodCliFat();
            Clientes cli = clientesDao.getClientesMobile(codfil, clifat, persistencia);
            retorno.add(cli);
            for (Rt_Guias guia : guias) {
                if (!clifat.equals(guia.getCodCliFat()) && !guia.getCodCliFat().equals("")) {
                    cli = clientesDao.getClientesMobile(codfil, guia.getCodCliFat(), persistencia);
                    retorno.add(cli);
                    clifat = guia.getCodCliFat();
                }
            }
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void enviarEmailManifest(EmailsEnviar email, Persistencia persistencia) throws Exception {
        try {
            File filePDFanexo = new File("C:/xampp/htdocs/satmobile/documentos/anexo-email/");
            filePDFanexo.mkdirs();
            // getHr_inclusao porque o nome do anexo está salvo aqui
            String anexo = email.getHr_Inclusao();
            OutputStream os = new FileOutputStream("C:/xampp/htdocs/satmobile/documentos/anexo-email/" + anexo);

            Tidy tidy = new Tidy();
            email.setMensagem(email.getMensagem().replace("https://mobile.sasw.com.br:9091/satellite/logos/", "http://localhost:9080/satellite/logos/"));
            InputStream input = new ByteArrayInputStream(email.getMensagem().getBytes());
            Document doc = tidy.parseDOM(input, null);
            ITextRenderer renderer = new ITextRenderer();
            renderer.setDocument(doc, null);
            renderer.layout();
            renderer.createPDF(os);
            input.close();
            os.close();

            EmailsEnviarDao emailDao = new EmailsEnviarDao();
            EmailsEnviarAnexoDao emailanexodao = new EmailsEnviarAnexoDao();

            String seqCli = emailDao.inserirEmail(email, persistencia);
            EmailsEnviarAnexo emailanexoCli = new EmailsEnviarAnexo();
            emailanexoCli.setSequencia(new BigDecimal(seqCli));
            emailanexoCli.setOrdem(1);
            emailanexoCli.setEndAnexo(anexo);
            emailanexoCli.setNomeAnexo(anexo);
            emailanexoCli.setDescAnexo(anexo);
            emailanexodao.inserirAnexos(emailanexoCli, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public EmailsEnviar geraManifest(String codfil, String sequencia, int p, String dataAtual, String horaAtual,
            String h, String idioma, String codCliFat, Persistencia persistencia) throws Exception {
        try {
            EmailsEnviar retorno = new EmailsEnviar();
            StringBuilder mensagem = new StringBuilder();
            String parada = String.valueOf(p), html = h;

            LocalDateTime dataFormatacao = LocalDateTime.parse(dataAtual + horaAtual, DateTimeFormatter.ofPattern("yyyyMMddHH:mm"));
            String dataFormatada = idioma.toUpperCase().equals("EN") ? dataFormatacao.format(DateTimeFormatter.ofPattern("MMM dd, yyyy hh:mm a", Locale.US))
                    : dataFormatacao.format(DateTimeFormatter.ofPattern("dd 'de' MMMM 'de' yyyy, HH:mm"));
            String dataFormatadaEmail = idioma.toUpperCase().equals("EN") ? dataFormatacao.format(DateTimeFormatter.ofPattern("MM-dd-yyyy", Locale.US))
                    : dataFormatacao.format(DateTimeFormatter.ofPattern("dd-MMM-yyyy"));

            Rt_GuiasDao rt_GuiasDao = new Rt_GuiasDao();
            CxFGuiasVolDao cxfguiasvolDao = new CxFGuiasVolDao();
            CxFGuiasDao cxfguiasDao = new CxFGuiasDao();
            CxForteDao cxforteDao = new CxForteDao();
            Rt_PercDao rt_PercDao = new Rt_PercDao();
            FiliaisDao filialdao = new FiliaisDao();

            Boolean pedido = rt_PercDao.existePedido(sequencia, parada, persistencia);
            String ER = rt_PercDao.ER(sequencia, parada, persistencia);
            List<Rt_Guias> guiasTotal, guias = new ArrayList<>();
            if (ER.equals("E")) {
                guiasTotal = rt_GuiasDao.listaGuiasEntrega(sequencia, parada, pedido, persistencia);
                guiasTotal.addAll(cxfguiasDao.listaGuiasEntrega(sequencia, parada, persistencia));
            } else {
                guiasTotal = rt_GuiasDao.listaGuias(sequencia, parada, pedido, persistencia);
            }

            CxForte cxforte = cxforteDao.getCxForte(new BigDecimal(codfil), persistencia);
            //Carregando informações sobre a filial
            Filiais filial = filialdao.getFilial(codfil, persistencia);

            String vNomefat;
            String vEmailfat;
            String vRota;
            String vNomeDestino;
            String vEndDestino;
            String vEmailDest;
            String vCodCliDest;

            if (!guiasTotal.isEmpty()) {
                String malotes = "";
                BigDecimal vQtdeVol = BigDecimal.ZERO;
                BigDecimal vValor = BigDecimal.ZERO;

                for (Rt_Guias guia : guiasTotal) {
                    if (codCliFat.equals(guia.getCodCliFat())) {
                        guias.add(guia);
                    }
                }

                for (Rt_Guias guia : guias) {
                    vValor = vValor.add(guia.getValor());

                    vNomefat = guia.getnRedFat();
                    vEmailfat = guia.getEmailFat();
                    vRota = guia.getRotaDst();
                    vNomeDestino = guia.getnRedDst();
                    vEndDestino = guia.getEndDst() + " " + guia.getBairroDst() + " " + guia.getCidadeDst() + " " + guia.getEstadoDst() + " " + guia.getCepDst();

                    List<CxFGuiasVol> volumes = cxfguiasvolDao.listaDeVolumes(guia.getGuia().toPlainString(), guia.getSerie(), persistencia);
                    for (CxFGuiasVol volume : volumes) {
                        malotes += "<h2>@TShipmentDetails</h2>"
                                + "            <hr style=\"width: 500px; float: left\" />"
                                + "            <div style=\"width: 100%;height: 1px;float: left;\"></div>"
                                + "            <table border=\"0\" cellpadding=\"1\" cellspacing=\"1\" style=\"width: 500px\">"
                                + "                <tbody>"
                                + "                    <tr>"
                                + "                        <td style=\"width: 250px;\">@TCustomerName</td>"
                                + "                        <td>" + guia.getnRedFat() + "</td>"
                                + "                    </tr>"
                                + "                    <tr>"
                                + "                        <td>@TPickUpLocation</td>"
                                + "                        <td>" + guia.getnRedOri() + "</td>"
                                + "                    </tr>"
                                + "                    <tr>"
                                + "                        <td>@TAddress</td>"
                                + "                        <td>" + guia.getEndOri() + ", " + guia.getCidadeOri() + "/" + guia.getEstadoOri() + "</td>"
                                + "                    </tr>"
                                + "                    <tr>"
                                + "                        <td>@TCommodityType</td>"
                                + "                        <td>" + "USD" + "</td>"
                                + "                    </tr>"
                                + "                    <tr>"
                                + "                        <td>@TQuantity</td>"
                                + "                        <td>" + volume.getQtde().replace(".0", "") + "</td>"
                                + "                    </tr>"
                                + "                    <tr>"
                                + "                        <td>@TValue</td>"
                                + "                        <td>" + FuncoesString.formatarStringMoeda(volume.getValor().toEngineeringString(), idioma) + "</td>"
                                + "                    </tr>"
                                + "                    <tr>"
                                + "                        <td>@TSerialNumber</td>"
                                + "                        <td>" + volume.getLacre() + "</td>"
                                + "                    </tr>"
                                + "                </tbody>"
                                + "            </table>";
                        vQtdeVol = vQtdeVol.add(new BigDecimal(volume.getQtde()));
                    }

                    html = html.replace("@nomefat", vNomefat);
                    html = html.replace("@rota", vRota);
                    html = html.replace("@nomeorigem", vNomefat);
                    html = html.replace("@emailorigem", vEmailfat);

                    html = html.replace("@nomedestino", vNomeDestino);
                    html = html.replace("@enderecodestino", vEndDestino);

                    vEmailDest = guia.getCodCli2().equals(guia.getCodCliDst()) ? (ER.equals("R") ? "" : guia.getEmailDst()) : cxforte.getEmail();
                    vCodCliDest = guia.getCodCli2().equals(guia.getCodCliDst()) ? (ER.equals("R") ? "" : guia.getCodCliDst()) : cxforte.getCodCli();

                    retorno.setDest_email(vEmailDest);
                    retorno.setCodCli(vCodCliDest);

                    html = html.replace("@emaildestino", vEmailDest);

                    html = html.replace("@CodigoDetalhe", malotes);
                    html = html.replace("@Nome", "");

                    retorno.setAssunto("CIT Manifest Report - " + guia.getnRedFat().replace("/", "-") + " " + dataFormatadaEmail + " " + sequencia + "." + parada + "." + dataFormatacao.toEpochSecond(ZoneOffset.UTC));
                    String anexo = EnvioEmail.anexo("MR " + persistencia.getEmpresa().replace("SAT", ""), guia.getnRedFat().replace("/", "-"), "-" + dataAtual + " " + sequencia + parada + ".pdf");
                    // Salvando o nome do anexo em hr_inclusao pra reuso depois
                    retorno.setHr_Inclusao(anexo);
                }

                String total = "<h2>@TTotals</h2>"
                        + "        <hr style=\"width: 500px; float: left\" />"
                        + "        <div style=\"width: 100%;height: 1px;float: left;\"></div>"
                        + "        <table border=\"0\" cellpadding=\"1\" cellspacing=\"1\" style=\"background-color: rgb(249, 249, 249); width: 500px;\">"
                        + "            <tbody>"
                        + "                <tr>"
                        + "                    <td style=\"width: 250px;\">@TTotalQuantity</td>"
                        + "                    <td>@TotalQtd</td>"
                        + "                </tr>"
                        + "                <tr>"
                        + "                    <td>@TTotalAmount</td>"
                        + "                    <td>@TotalValor</td>"
                        + "                </tr>"
                        + "            </tbody>"
                        + "        </table>";

                total = total.replace("@TotalValor", FuncoesString.formatarStringMoeda(vValor.toEngineeringString(), idioma));
                total = total.replace("@TotalQtd", vQtdeVol.toBigInteger().toString());

                html = html.replace("@Total", total);

                switch (idioma) {
                    case "en":
                        html = html.replace("@dataentrega", dataFormatada);
                        html = html.replace("@dataremessa", dataAtual + "-" + dataFormatacao.toEpochSecond(ZoneOffset.UTC));

                        html = html.replace("@TShipmentDetails", "Shipment Details");
                        html = html.replace("@TTotals", "Totals");
                        html = html.replace("@TTotalQuantity", "Total Quantity");
                        html = html.replace("@TTotalAmount", "Total Amount");
                        html = html.replace("@TSignature", "Signature");
                        html = html.replace("@TName", "Messenger's Name");
                        html = html.replace("@TCustomerName", "Customer Name");
                        html = html.replace("@TPickUpLocation", "Pick Up Location");
                        html = html.replace("@TAddress", "Address");
                        html = html.replace("@TCommodityType", "Commodity Type");
                        html = html.replace("@TQuantity", "Quantity");
                        html = html.replace("@TValue", "Value");
                        html = html.replace("@TSerialNumber", "Serial Number");
                        break;
                    default:
                        html = html.replace("@dataentrega", dataFormatada);
                        html = html.replace("@dataremessa", dataAtual + "-" + dataFormatacao.toEpochSecond(ZoneOffset.UTC));

                        html = html.replace("@TShipmentDetails", "Detalhes do Envio");
                        html = html.replace("@TTotals", "Total");
                        html = html.replace("@TTotalQuantity", "Quantidade Total");
                        html = html.replace("@TTotalAmount", "Valor Total");
                        html = html.replace("@TSignature", "Assintaura");
                        html = html.replace("@TName", "Nome");
                        html = html.replace("@TCustomerName", "Nome do Cliente");
                        html = html.replace("@TPickUpLocation", "Local da Coleta");
                        html = html.replace("@TAddress", "Endereço");
                        html = html.replace("@TCommodityType", "Tipo");
                        html = html.replace("@TQuantity", "Quantidade");
                        html = html.replace("@TValue", "Valor");
                        html = html.replace("@TSerialNumber", "Número de Série");
                }

                html = html.replace("@ImagemLogo", getLogo(persistencia.getEmpresa(), codfil));
                mensagem = mensagem.append(html);

                retorno.setSmtp("smtplw.com.br");
                retorno.setRemet_email("<EMAIL>");
                retorno.setRemet_nome(filial.getRazaoSocial());

                // email.setDest_email("<EMAIL>");
                retorno.setDest_nome(persistencia.getEmpresa().replace("SAT", ""));
                retorno.setMensagem(html);
                retorno.setAut_login("sasw");
                retorno.setAut_senha("xNiadJEj9607");
                retorno.setPorta(587);

                retorno.setParametro(persistencia.getEmpresa());
                retorno.setCodFil(codfil);

            } else {
                /* Tratamento erro na geração do report */
                ClientesDao clientesDao = new ClientesDao();
                ClientesGuia cliente = clientesDao.listaClientesEntrega(sequencia, parada, persistencia);

                html = html.replace("@rota", "" + cliente.getParada());
                html = html.replace("@nomeorigem", cliente.getNredOrigem());
                html = html.replace("@emailorigem", cliente.getEmailOrigem());

                html = html.replace("@nomedestino", cliente.getNredDestino());
                html = html.replace("@enderecodestino", cliente.getEnderecoDestino());
                html = html.replace("@emaildestino", cliente.getEmailDestino());

                html = html.replace("@CodigoDetalhe", " ");
                html = html.replace("@Nome", "");

                String total = "<br></br>"
                        + "        <hr style=\"width: 500px; float: left\" />"
                        + "        <div style=\"width: 100%;height: 1px;float: left;\"></div>"
                        + "        <h2 style=\"text-align: center; width:500px\" width=\"500px\"> *** NO SERVICE ORDER FOUND *** </h2>"
                        + "        <hr style=\"width: 500px; float: left\" />"
                        + "        <div style=\"width: 100%;height: 1px;float: left;\"></div>";

                html = html.replace("@Total", total);
                switch (idioma) {
                    case "en":
                        html = html.replace("@dataentrega", dataFormatacao.format(DateTimeFormatter.ofPattern("MMM dd, yyyy hh:mm a", Locale.US)));
                        html = html.replace("@dataremessa", dataAtual + "-" + dataFormatacao.toEpochSecond(ZoneOffset.UTC));

                        html = html.replace("@TShipmentDetails", "Shipment Details");
                        html = html.replace("@TTotals", "Totals");
                        html = html.replace("@TTotalQuantity", "Total Quantity");
                        html = html.replace("@TTotalAmount", "Total Amount");
                        html = html.replace("@TSignature", "Signature");
                        html = html.replace("@TName", "Messenger's Name");
                        html = html.replace("@TCustomerName", "Customer Name");
                        html = html.replace("@TPickUpLocation", "Pick Up Location");
                        html = html.replace("@TAddress", "Address");
                        html = html.replace("@TCommodityType", "Commodity Type");
                        html = html.replace("@TQuantity", "Quantity");
                        html = html.replace("@TValue", "Value");
                        html = html.replace("@TSerialNumber", "Serial Number");
                        break;
                    default:
                        html = html.replace("@dataentrega", dataFormatacao.format(DateTimeFormatter.ofPattern("dd de MMMM de yyyy, HH:mm")));
                        html = html.replace("@dataremessa", dataAtual + "-" + dataFormatacao.toEpochSecond(ZoneOffset.UTC));

                        html = html.replace("@TShipmentDetails", "Detalhes do Envio");
                        html = html.replace("@TTotals", "Total");
                        html = html.replace("@TTotalQuantity", "Quantidade Total");
                        html = html.replace("@TTotalAmount", "Valor Total");
                        html = html.replace("@TSignature", "Assintaura");
                        html = html.replace("@TName", "Nome");
                        html = html.replace("@TCustomerName", "Nome do Cliente");
                        html = html.replace("@TPickUpLocation", "Local da Coleta");
                        html = html.replace("@TAddress", "Endereço");
                        html = html.replace("@TCommodityType", "Tipo");
                        html = html.replace("@TQuantity", "Quantidade");
                        html = html.replace("@TValue", "Valor");
                        html = html.replace("@TSerialNumber", "Número de Série");
                }

                html = html.replace("@ImagemLogo", getLogo(persistencia.getEmpresa(), codfil));
                mensagem = mensagem.append(html);
            }

            retorno.setMensagem(mensagem.toString());
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    private String getLogo(String empresa, String codfil) {
        String url;
        if (codfil.contains(".0")) {
            codfil = codfil.replace(".0", "");
        }
        switch (empresa) {
            case "SATGLOVAL":
            case "BKSATGLOVAL":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/gloval_manifest.jpg";
                break;
            case "SATCOGAR":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/LogoCogar.jpg";
                break;
            case "CONFEDERAL":
            case "CONFEDERALGO":
            case "SATCONFEDERALBSB":
            case "SATCONFEDERALGO":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_confederal.jpg";
                break;
            case "SATCORPVS":
            case "SATCORPVSPE":
            case "SATCORPVS2":
            case "SATCORPVSPE2":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_CPV.jpg";
                break;
            case "SATTRANSVIP":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/LogoTVip.jpg";
                break;
            case "SATPRESERVE":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_preserve.jpg";
                break;
            case "VSG":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_VSG.jpg";
                break;
            case "SATTSEG":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_tecnoseg.jpg";
                break;
            case "SATAGIL":
            case "AGILSERV":
            case "SATAGILVIG":
            case "SATAGILCOND":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_AGIL.jpg";
                break;
            case "SATLOYAL":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_Loyal.jpg";
                break;
            case "SATTRANSVIG":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_transvig.jpg";
                break;
            case "SATINVLMT":
            case "SATINVLRS":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_invioseg.jpg";
                break;
            case "SATGSI":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_GSI.jpg";
                break;
            case "SATCIT":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_citcompany.png";
                break;
            case "SATSHALOM":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_shalom.png";
                break;
             case "SATBRINKS":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/Logo_Brinks.jpg";
                break;                
            case "SATTRANSEXCEL":
                try {
                    switch (codfil) {
                        case "1":
                            url = "https://mobile.sasw.com.br:9091/satellite/logos/logotransexcel.jpg";
                            break;
                        case "2001":
                            url = "https://mobile.sasw.com.br:9091/satellite/logos/logoDepa.jpg";
                            break;
                        case "3001":
                            url = "https://mobile.sasw.com.br:9091/satellite/logos/logoExcel.jpg";
                            break;
                        default:
                            url = "https://mobile.sasw.com.br:9091/satellite/logos/logotransexcel.jpg";
                    }
                } catch (Exception e) {
                    url = "https://mobile.sasw.com.br:9091/satellite/logos/logotransexcel.jpg";
                }
                break;
            case "SATRODOB":
            case "SATRODOBAN":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/LogoRDB.jpg";
                break;
            case "SATTAMEME":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_tameme.jpg";
                break;
            case "SATCOMETRA":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_GSI.jpg";
                break;
            case "SATSASEX":
            case "SASEX":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/LogoSASEX.jpg";
                break;
            case "EAGSATI":
            case "EAGSAS":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/eagle.jpg";
                break;
            case "SAS":
            case "SASW":
            case "SATELLITE":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logosas.jpg";
                break;
            case "SATQUALIFOCO":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_qualifoco.jpg";
                break;
            default:
                url = "";
        }
        return url;
    }

    private String getLogoAnexo(String empresa, String codfil) {
        String url;
        if (codfil.contains(".0")) {
            codfil = codfil.replace(".0", "");
        }
        switch (empresa) {
            case "SATGLOVAL":
            case "BKSATGLOVAL":
                url = "http://localhost:9080/satellite/logos/gloval_manifest.jpg";
                break;
            case "SATCOGAR":
                url = "http://localhost:9080/satellite/logos/LogoCogar.jpg";
                break;
            case "CONFEDERAL":
            case "CONFEDERALGO":
            case "SATCONFEDERALBSB":
            case "SATCONFEDERALGO":
                url = "http://localhost:9080/satellite/logos/logo_confederal.jpg";
                break;
            case "SATCORPVS":
            case "SATCORPVSPE":
                url = "http://localhost:9080/satellite/logos/logo_CPV.jpg";
                break;
            case "SATTRANSVIP":
                url = "http://localhost:9080/satellite/logos/LogoTVip.jpg";
                break;
            case "SATPRESERVE":
                url = "http://localhost:9080/satellite/logos/logo_preserve.jpg";
                break;
            case "VSG":
                url = "http://localhost:9080/satellite/logos/logo_VSG.jpg";
                break;
            case "SATTSEG":
                url = "http://localhost:9080/satellite/logos/logo_tecnoseg.jpg";
                break;
            case "SATAGIL":
            case "AGILSERV":
            case "SATAGILVIG":
            case "SATAGILCOND":
                url = "http://localhost:9080/satellite/logos/logo_AGIL.jpg";
                break;
            case "SATLOYAL":
                url = "http://localhost:9080/satellite/logos/logo_Loyal.jpg";
                break;
            case "SATTRANSVIG":
                url = "http://localhost:9080/satellite/logos/logo_transvig.jpg";
                break;
            case "SATINVLMT":
            case "SATINVLRS":
                url = "http://localhost:9080/satellite/logos/logo_invioseg.jpg";
                break;
            case "SATGSI":
                url = "http://localhost:9080/satellite/logos/logo_GSI.jpg";
                break;
            case "SATTRANSEXCEL":
                try {
                    switch (codfil) {
                        case "1":
                            url = "http://localhost:9080/satellite/logos/logotransexcel.jpg";
                            break;
                        case "2001":
                            url = "http://localhost:9080/satellite/logos/logoDepa.jpg";
                            break;
                        case "3001":
                            url = "http://localhost:9080/satellite/logos/logoExcel.jpg";
                            break;
                        default:
                            url = "http://localhost:9080/satellite/logos/logotransexcel.jpg";
                    }
                } catch (Exception e) {
                    url = "http://localhost:9080/satellite/logos/logotransexcel.jpg";
                }
                break;
            case "SATRODOB":
            case "SATRODOBAN":
                url = "http://localhost:9080/satellite/logos/LogoRDB.jpg";
                break;
            case "SATTAMEME":
                url = "http://localhost:9080/satellite/logos/logo_tameme.jpg";
                break;
            case "SATCOMETRA":
                url = "http://localhost:9080/satellite/logos/logo_GSI.jpg";
                break;
            case "SATSASEX":
            case "SASEX":
                url = "http://localhost:9080/satellite/logos/LogoSASEX.jpg";
                break;
            case "EAGSATI":
            case "EAGSAS":
                url = "http://localhost:9080/satellite/logos/eagle.jpg";
                break;
            case "SAS":
            case "SASW":
            case "SATELLITE":
                url = "http://localhost:9080/satellite/logos/logosas.jpg";
                break;
            case "SATQUALIFOCO":
                url = "http://localhost:9080/satellite/logos/logo_qualifoco.jpg";
                break;
            case "SATCIT":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_citcompany.png";
                break;
            case "SATBRINKS":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/Logo_Brinks.jpg";
                break;                    
            case "SATSHALOM":
                url = "https://mobile.sasw.com.br:9091/satellite/logos/logo_shalom.png";
                break;
            default:
                url = "";
        }
        return url;
    }

    public List<Rt_Guias> listarGuias(BigDecimal sequencia, int parada, Persistencia persistencia) throws Exception {
        try {
            Rt_GuiasDao rt_guiasdao = new Rt_GuiasDao();
            return rt_guiasdao.listaGuiasWeb(sequencia, parada, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<EGtv> listarGuias(Map filtros, Persistencia persistencia) throws Exception {
        try {
            EGtvDao guiasClienteDAO = new EGtvDao();
            return guiasClienteDAO.lista(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Rt_Hist> listarHistorico(BigDecimal sequencia, int parada, Persistencia persistencia) throws Exception {
        try {
            Rt_HistDao rt_histdao = new Rt_HistDao();
            return rt_histdao.listarHistorico(sequencia, parada, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista os trajetos
     *
     * @param sequencia sequencia da rota
     * @param excl
     * @param persistencia conexão com a base de dados
     * @return lista contendo o trajeto
     * @throws Exception
     */
    public List<Rt_Perc> listarTrajetos(BigDecimal sequencia, Boolean excl, Persistencia persistencia) throws Exception {
        List<Rt_Perc> trajetos = new ArrayList<>();
        try {
            Rt_PercDao rt_PercDao = new Rt_PercDao();
            //trajetos = rt_PercDao.listarTrajetos(sequencia, persistencia);
            trajetos = rt_PercDao.listarTrajetosSatMobWeb(sequencia, excl, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
        return trajetos;
    }

    /**
     * Lista os trajetos
     *
     * @param sequencia sequencia da rota
     * @param excl
     * @param persistencia conexão com a base de dados
     * @return lista contendo o trajeto
     * @throws Exception
     */
    public List<Rt_Perc> listarTrajetosSemCoordenada(BigDecimal sequencia, Boolean excl, Persistencia persistencia) throws Exception {
        List<Rt_Perc> trajetos = new ArrayList<>();
        try {
            Rt_PercDao rt_PercDao = new Rt_PercDao();
            //trajetos = rt_PercDao.listarTrajetos(sequencia, persistencia);
            trajetos = rt_PercDao.listarTrajetosSatMobWebSemCoordenada(sequencia, excl, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
        return trajetos;
    }

    /**
     *
     * @param codFil
     * @param codPessoa
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<CtrOperEquip> listarPosicoesContainers(String codFil, String codNivel, String codPessoa, Persistencia persistencia) throws Exception {
        try {
            CtrOperEquipDao ctrOperEquipDao = new CtrOperEquipDao();
            return ctrOperEquipDao.listarPosicaoContainers(codFil, codNivel, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista posições de containers com filtros dinâmicos
     * @param codFil
     * @param codNivel
     * @param codPessoa
     * @param filtros
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<CtrOperEquip> listarPosicoesContainersFiltrado(String codFil, String codNivel, String codPessoa,
            Map<String, List<String>> filtros, Persistencia persistencia) throws Exception {
        try {
            CtrOperEquipDao ctrOperEquipDao = new CtrOperEquipDao();
            return ctrOperEquipDao.listarPosicoesContainersFiltrado(codFil, codNivel, codPessoa, filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista bairros para filtro de containers
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<String> listarBairrosContainers(Persistencia persistencia) throws Exception {
        try {
            CtrOperEquipDao ctrOperEquipDao = new CtrOperEquipDao();
            return ctrOperEquipDao.listarBairrosContainers(persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista solicitantes para filtro de containers
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<String> listarSolicitantesContainers(Persistencia persistencia) throws Exception {
        try {
            CtrOperEquipDao ctrOperEquipDao = new CtrOperEquipDao();
            return ctrOperEquipDao.listarSolicitantesContainers(persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista clientes para filtro de containers
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<String> listarClientesContainers(Persistencia persistencia) throws Exception {
        try {
            CtrOperEquipDao ctrOperEquipDao = new CtrOperEquipDao();
            return ctrOperEquipDao.listarClientesContainers(persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista clientes de serviço para filtro de containers
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<String> listarClientesServicoContainers(Persistencia persistencia) throws Exception {
        try {
            CtrOperEquipDao ctrOperEquipDao = new CtrOperEquipDao();
            return ctrOperEquipDao.listarClientesServicoContainers(persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }
    
    /**
     *
     * @param dataPesquisa
     * @param codPessoa
     * @param dataHoje
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<CtrOperEquip> listarPosicaoContainersMarketing(String codPessoa, String dataPesquisa, String dataHoje, Persistencia persistencia) throws Exception {
        try {
            CtrOperEquipDao ctrOperEquipDao = new CtrOperEquipDao();
            return ctrOperEquipDao.listarPosicaoContainersMarketing(codPessoa, dataPesquisa, dataHoje, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista os trajetos de entrega para uma parada de recolhimento
     *
     * @param sequencia sequencia da rota
     * @param codCli2
     * @param hora1
     * @param persistencia conexão com a base de dados
     * @return lista contendo o trajeto
     * @throws Exception
     */
    public List<Rt_Perc> listarEntregas(BigDecimal sequencia, String codCli2, String hora1, Persistencia persistencia) throws Exception {
        try {
            Rt_PercDao rt_PercDao = new Rt_PercDao();
            return rt_PercDao.listarServicosEntrega(sequencia.toString(), codCli2, hora1, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista de rotas em supervisão
     *
     * @param codfil Código da filial
     * @param persistencia Conexão com banco de dados
     * @return lista de rotas do banco
     * @throws Exception
     */
    public List<Rotas> listarRotas(String codfil, Persistencia persistencia) throws Exception {
        List<Rotas> rotas = new ArrayList<>();
        try {
            RotasDao rotasDao = new RotasDao();
            rotas = rotasDao.listarRotas(codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
        return rotas;
    }

    public List<Rotas> listarRotasData(String codfil, String data, Boolean excl, BigDecimal codpessoa, Persistencia persistencia) throws Exception {
        List<Rotas> rotas = new ArrayList<>();
        try {
            RotasDao rotasDao = new RotasDao();
            rotas = rotasDao.listarRotasData(codfil, data, excl, codpessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
        return rotas;
    }

    /**
     * Lista as rotas do dia
     *
     * @param codfil
     * @param data
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Rotas> listarRotasData(String codfil, String data, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            return rotasDao.listarRotasData(codfil, data, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Lista os veículos da filial
     *
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Veiculos> listarVeiculos(String codFil, Persistencia persistencia) throws Exception {
        try {
            VeiculosDao veiculosDao = new VeiculosDao();
            return veiculosDao.getVeiculos(codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Cria uma nova rota e retorna o valor de SeqRota.
     *
     * @param rota
     * @param persistencia
     * @return
     * @throws Exception
     */
    public int criarRotaTransporte(Rotas rota, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            int seqRota = 0;
            rota.setFlag_Excl("");
            rota = (Rotas) FuncoesString.removeAcentoObjeto(rota);
            try {
                for (int i = 0; i < 20; i++) {
                    seqRota = rotasDao.MaxSequencia(persistencia).intValue() + 1;
                    rota.setSequencia(String.valueOf(seqRota));
                    try {
                        rotasDao.inserirRota(rota, persistencia);
                    } catch (Exception e1) {
                        seqRota = 0;
                    } finally {
                        if (seqRota != 0) {
                            break;
                        }
                    }
                }
                if (seqRota == 0) {
                    throw new Exception("TentativasExcedidas");
                }
            } catch (Exception e2) {
                throw new Exception(e2);
            }
            return seqRota;
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    /**
     * Faz a edição das informações de uma rota
     *
     * @param rota
     * @param persistencia
     * @throws Exception
     */
    public void editarRota(Rotas rota, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            rotasDao.atualizarRota(rota, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca pessoas para o cadastro de escala
     *
     * @param query
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Pessoa> buscarPessoaEscala(String query, String codFil, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoaDao = new PessoaDao();
            return pessoaDao.buscarPessoaEscala(query, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca pessoas para o cadastro de escala
     *
     * @param query
     * @param funcao
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Pessoa> buscarPessoaEscalaFuncao(String query, String funcao, String codFil, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoaDao = new PessoaDao();
            return pessoaDao.buscarPessoaEscalaFuncao(query, funcao, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Pessoa buscarMatricula(String matr, Persistencia persistencia) throws Exception {
        try {
            PessoaDao pessoadao = new PessoaDao();
            return pessoadao.buscaMatr(matr, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Veiculos buscarVeiculo(int numero, Persistencia persistencia) throws Exception {
        try {
            VeiculosDao veiculosDao = new VeiculosDao();
            return veiculosDao.BuscaVeiculoNumero(numero, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Verifica a existência de alguma outra escala para a data.
     *
     * @param codfil
     * @param data
     * @param matr
     * @param seqRota
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean existeEscala(String codfil, String data, String matr, String seqRota, Persistencia persistencia) throws Exception {
        try {
            String sequencia = EscalaDao.obterSequenciaRota(codfil, data, matr, persistencia);
            return !(null == sequencia || sequencia.replace(".0", "").equals(seqRota.replace(".0", "")));
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca a escala de um veículo
     *
     * @param data
     * @param veiculo
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Escala existeEscalaVeiculo(String data, String veiculo, Persistencia persistencia) throws Exception {
        try {
            EscalaDao escalaDao = new EscalaDao();
            return escalaDao.escalaVeiculo(data, veiculo, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void inserirEscala(Escala escala, Persistencia persistencia) throws Exception {
        try {
            EscalaDao escalaDao = new EscalaDao();
            escalaDao.cadastrarEscala(escala, persistencia);

            escala.setDt_Alter(LocalDate.now());
            escala.setHr_Alter(getDataAtual("HORA"));
            escalaDao.atualizarEscala(escala, persistencia);

            Rt_EscalaDao rt_EscalaDao = new Rt_EscalaDao();
            Rt_Escala rt_Escala;
            if (escala.getMatrMot().compareTo(BigDecimal.ZERO) > 0) {
                rt_Escala = new Rt_Escala();
                rt_Escala.setData(escala.getData());
                rt_Escala.setDt_alter(getDataAtual("SQL"));
                rt_Escala.setFuncao("M");
                rt_Escala.setHora(escala.getHrMot());
                rt_Escala.setHr_alter(getDataAtual("HORA"));
                rt_Escala.setMatr(escala.getMatrMot());
                rt_Escala.setOperador(escala.getOperador());
                rt_Escala.setSequencia(escala.getSeqRota());
                rt_EscalaDao.criarEscala(rt_Escala, persistencia);
            }

            if (escala.getMatrChe().compareTo(BigDecimal.ZERO) > 0) {
                rt_Escala = new Rt_Escala();
                rt_Escala.setData(escala.getData());
                rt_Escala.setDt_alter(getDataAtual("SQL"));
                rt_Escala.setFuncao("C");
                rt_Escala.setHora(escala.getHrChe());
                rt_Escala.setHr_alter(getDataAtual("HORA"));
                rt_Escala.setMatr(escala.getMatrChe());
                rt_Escala.setOperador(escala.getOperador());
                rt_Escala.setSequencia(escala.getSeqRota());
                rt_EscalaDao.criarEscala(rt_Escala, persistencia);
                
                /* Criar primeir a posição da Rota Lat/Lon da Central (Caixa Forte)
                >> INICIO */
                RastrearDao rastrearDao = new RastrearDao();
                Rastrear posicoesCentral = rastrearDao.posicaoCentral(escala.getSeqRota(), persistencia);
                rastrearDao.inserirPosicaoInicial(rt_Escala, Double.valueOf(posicoesCentral.getLatitude()), Double.valueOf(posicoesCentral.getLongitude()), persistencia);
            }

            if (escala.getMatrVig1().compareTo(BigDecimal.ZERO) > 0) {
                rt_Escala = new Rt_Escala();
                rt_Escala.setData(escala.getData());
                rt_Escala.setDt_alter(getDataAtual("SQL"));
                rt_Escala.setFuncao("V");
                rt_Escala.setHora(escala.getHrVig1());
                rt_Escala.setHr_alter(getDataAtual("HORA"));
                rt_Escala.setMatr(escala.getMatrVig1());
                rt_Escala.setOperador(escala.getOperador());
                rt_Escala.setSequencia(escala.getSeqRota());
                rt_EscalaDao.criarEscala(rt_Escala, persistencia);
            }

            if (escala.getMatrVig2().compareTo(BigDecimal.ZERO) > 0) {
                rt_Escala = new Rt_Escala();
                rt_Escala.setData(escala.getData());
                rt_Escala.setDt_alter(getDataAtual("SQL"));
                rt_Escala.setFuncao("V");
                rt_Escala.setHora(escala.getHrVig2());
                rt_Escala.setHr_alter(getDataAtual("HORA"));
                rt_Escala.setMatr(escala.getMatrVig2());
                rt_Escala.setOperador(escala.getOperador());
                rt_Escala.setSequencia(escala.getSeqRota());
                rt_EscalaDao.criarEscala(rt_Escala, persistencia);
            }

            if (escala.getMatrVig3().compareTo(BigDecimal.ZERO) > 0) {
                rt_Escala = new Rt_Escala();
                rt_Escala.setData(escala.getData());
                rt_Escala.setDt_alter(getDataAtual("SQL"));
                rt_Escala.setFuncao("V");
                rt_Escala.setHora(escala.getHrVig3());
                rt_Escala.setHr_alter(getDataAtual("HORA"));
                rt_Escala.setMatr(escala.getMatrVig3());
                rt_Escala.setOperador(escala.getOperador());
                rt_Escala.setSequencia(escala.getSeqRota());
                rt_EscalaDao.criarEscala(rt_Escala, persistencia);
            }

        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void atualizarEscala(Escala escala, Persistencia persistencia) throws Exception {
        try {
            EscalaDao escalaDao = new EscalaDao();

            escala.setDt_Alter(LocalDate.now());
            escala.setHr_Alter(getDataAtual("HORA"));
            escalaDao.atualizarEscala(escala, persistencia);

            Rt_EscalaDao rt_EscalaDao = new Rt_EscalaDao();
            Rt_Escala rt_Escala;
            if (escala.getMatrMot().compareTo(BigDecimal.ZERO) > 0) {
                rt_Escala = new Rt_Escala();
                rt_Escala.setData(escala.getData());
                rt_Escala.setDt_alter(getDataAtual("SQL"));
                rt_Escala.setFuncao("M");
                rt_Escala.setHora(escala.getHrMot());
                rt_Escala.setHr_alter(getDataAtual("HORA"));
                rt_Escala.setMatr(escala.getMatrMot());
                rt_Escala.setOperador(escala.getOperador());
                rt_Escala.setSequencia(escala.getSeqRota());
                if (rt_EscalaDao.SelecionaRt_EscalaSatMobWeb(escala.getSeqRota(), persistencia).getSequencia().compareTo(BigDecimal.ZERO) == 0) {
                    rt_EscalaDao.criarEscala(rt_Escala, persistencia);
                } else {
                    rt_EscalaDao.atualizarEscala(rt_Escala, persistencia);
                }
            }

            if (escala.getMatrChe().compareTo(BigDecimal.ZERO) > 0) {
                rt_Escala = new Rt_Escala();
                rt_Escala.setData(escala.getData());
                rt_Escala.setDt_alter(getDataAtual("SQL"));
                rt_Escala.setFuncao("C");
                rt_Escala.setHora(escala.getHrChe());
                rt_Escala.setHr_alter(getDataAtual("HORA"));
                rt_Escala.setMatr(escala.getMatrChe());
                rt_Escala.setOperador(escala.getOperador());
                rt_Escala.setSequencia(escala.getSeqRota());
                if (rt_EscalaDao.SelecionaRt_EscalaSatMobWeb(escala.getSeqRota(), persistencia).getSequencia().compareTo(BigDecimal.ZERO) == 0) {
                    rt_EscalaDao.criarEscala(rt_Escala, persistencia);
                } else {
                    rt_EscalaDao.atualizarEscala(rt_Escala, persistencia);
                }
            }


            if (escala.getMatrVig1().compareTo(BigDecimal.ZERO) > 0) {
                rt_Escala = new Rt_Escala();
                rt_Escala.setData(escala.getData());
                rt_Escala.setDt_alter(getDataAtual("SQL"));
                rt_Escala.setFuncao("V");
                rt_Escala.setHora(escala.getHrVig1());
                rt_Escala.setHr_alter(getDataAtual("HORA"));
                rt_Escala.setMatr(escala.getMatrVig1());
                rt_Escala.setOperador(escala.getOperador());
                rt_Escala.setSequencia(escala.getSeqRota());
                if (rt_EscalaDao.SelecionaRt_EscalaSatMobWeb(escala.getSeqRota(), persistencia).getSequencia().compareTo(BigDecimal.ZERO) == 0) {
                    rt_EscalaDao.criarEscala(rt_Escala, persistencia);
                } else {
                    rt_EscalaDao.atualizarEscala(rt_Escala, persistencia);
                }
            }

            if (escala.getMatrVig2().compareTo(BigDecimal.ZERO) > 0) {
                rt_Escala = new Rt_Escala();
                rt_Escala.setData(escala.getData());
                rt_Escala.setDt_alter(getDataAtual("SQL"));
                rt_Escala.setFuncao("V");
                rt_Escala.setHora(escala.getHrVig2());
                rt_Escala.setHr_alter(getDataAtual("HORA"));
                rt_Escala.setMatr(escala.getMatrVig2());
                rt_Escala.setOperador(escala.getOperador());
                rt_Escala.setSequencia(escala.getSeqRota());
                if (rt_EscalaDao.SelecionaRt_EscalaSatMobWeb(escala.getSeqRota(), persistencia).getSequencia().compareTo(BigDecimal.ZERO) == 0) {
                    rt_EscalaDao.criarEscala(rt_Escala, persistencia);
                } else {
                    rt_EscalaDao.atualizarEscala(rt_Escala, persistencia);
                }
            }

            if (escala.getMatrVig3().compareTo(BigDecimal.ZERO) > 0) {
                rt_Escala = new Rt_Escala();
                rt_Escala.setData(escala.getData());
                rt_Escala.setDt_alter(getDataAtual("SQL"));
                rt_Escala.setFuncao("V");
                rt_Escala.setHora(escala.getHrVig3());
                rt_Escala.setHr_alter(getDataAtual("HORA"));
                rt_Escala.setMatr(escala.getMatrVig3());
                rt_Escala.setOperador(escala.getOperador());
                rt_Escala.setSequencia(escala.getSeqRota());
                if (rt_EscalaDao.SelecionaRt_EscalaSatMobWeb(escala.getSeqRota(), persistencia).getSequencia().compareTo(BigDecimal.ZERO) == 0) {
                    rt_EscalaDao.criarEscala(rt_Escala, persistencia);
                } else {
                    rt_EscalaDao.atualizarEscala(rt_Escala, persistencia);
                }
            }

        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Busca as informações de paramet para criação da escala
     *
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public Paramet buscarParamet(String codFil, Persistencia persistencia) throws Exception {
        try {
            ParametDao parametDao = new ParametDao();
            return parametDao.getParametFilial(codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Verifica se algum funcionário está de folga.
     *
     * @param matrs
     * @param data
     * @param persistencia
     * @return
     * @throws Exception
     */
    public List<Funcion> funcionariosFolga(List<String> matrs, String data, Persistencia persistencia) throws Exception {
        try {
            if (matrs.isEmpty()) {
                return new ArrayList<>();
            }
            FuncionDao funcionDao = new FuncionDao();
            return funcionDao.folgas(matrs, data, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Verifica se a pessoa possui permissão de rotas no mobile
     *
     * @param codpessoa
     * @param persistencia
     * @return
     * @throws Exception
     */
    public boolean buscarPermissaoRotas(BigDecimal codpessoa, Persistencia persistencia) throws Exception {
        try {
            LoginDao loginDao = new LoginDao();
            return loginDao.PermissaoPessoaRota(codpessoa, persistencia).getSaspwac().getSistema().compareTo(new BigDecimal("300001")) == 0;
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Criar rotas para supervisãoo
     *
     * @param rotas objetos contendo informações de rota - Campos necessários:
     * sequencia, rota, data, codfil, TpVeic, Viagem, bacen, aeroporto,
     * hrlargada, hrchegada, hrintini, hrintfim, observacao, operador, dt_alter,
     * hr_alter
     * @param rt_Escala objetos contendo informaçôes de rt_escala - Campos
     * necessários: sequencia, matr, data, funcao, hora, operador, dt_alter,
     * hr_alter
     * @param escala objetos contendo informaçôes de rt_escala - Campos
     * necessários: rota, data, codfil, hora1, hora2, hora3, hora4, hstot,
     * hsinterv, matrmot, matrChe, codpessoaSup, seqrota, operador, dt_alter,
     * hr_alter
     * @param persistencia Conexão com a base de dados
     * @throws Exception
     */
    public void criarRotaSupervisao(Rotas rotas, Rt_Escala rt_Escala, Escala escala, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            Rt_EscalaDao rt_EscalaDao = new Rt_EscalaDao();
            EscalaDao escalaDao = new EscalaDao();

            //Criando uma nova rota
            //Criando uma nova sequencia
            String sequencia = String.valueOf(rotasDao.MaxSequencia(persistencia).intValue() + 1);
            rotas.setSequencia(sequencia);

            //Inserindo campos
            rotas.setViagem("N");
            rotas.setATM("N");
            rotas.setBACEN("N");
            rotas.setAeroporto("N");
            rotas.setFlag_Excl("");
            rotas = (Rotas) FuncoesString.removeAcentoObjeto(rotas);
            rotasDao.inserirRota(rotas, persistencia);

            //Criando em RT_Escala
            rt_Escala.setSequencia(rotas.getSequencia());
            rt_Escala.setData(tratarValores(rotas.getData()));
            rt_Escala.setFuncao("C");
            rt_Escala.setHora(rotas.getHrLargada());
            rt_Escala.setOperador(rotas.getOperador());
            rt_Escala.setDt_alter(tratarValores(rotas.getDt_Alter().toString()));
            rt_Escala.setHr_alter(rotas.getHr_Alter());
            rt_Escala = (Rt_Escala) FuncoesString.removeAcentoObjeto(rt_Escala);
            rt_EscalaDao.criarEscala(rt_Escala, persistencia);

            //Criando escala Escala
            escala.setRota(rotas.getRota());
            escala.setMatrMot("0");
            escala.setHora1(rotas.getHrLargada());
            escala.setHora2(rotas.getHrIntIni());
            escala.setHora3(rotas.getHrIntFim());
            escala.setHora4(rotas.getHrChegada());
            escala.setHsTot(rotas.getHsTotal().toPlainString());
            escala.setSeqRota(rotas.getSequencia().toPlainString());
            escala.setOperador(rotas.getOperador());
            escala.setDt_Alter(rotas.getDt_Alter());
            escala.setHr_Alter(rotas.getHr_Alter());
            escala = (Escala) FuncoesString.removeAcentoObjeto(escala);
            escalaDao.inserirEscalaSupervisaoRotaSatMobWeb(escala, tratarValores(rotas.getData()), persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Atualizar rotas para supervisãoo
     *
     * @param rotas objetos contendo informações de rota - Campos necessários:
     * sequencia, rota, data, codfil, TpVeic, Viagem, bacen, aeroporto,
     * hrlargada, hrchegada, hrintini, hrintfim, observacao, operador, dt_alter,
     * hr_alter
     * @param rt_Escala objetos contendo informaçôes de rt_escala - Campos
     * necessários: sequencia, matr, data, funcao, hora, operador, dt_alter,
     * hr_alter
     * @param escala objetos contendo informaçôes de rt_escala - Campos
     * necessários: rota, data, codfil, hora1, hora2, hora3, hora4, hstot,
     * hsinterv, matrmot, matrChe, codpessoaSup, seqrota, operador, dt_alter,
     * hr_alter
     * @param persistencia Conexão com a base de dados
     * @throws Exception
     */
    public void atualizarRotaSupervisao(Rotas rotas, Rt_Escala rt_Escala, Escala escala, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            Rt_EscalaDao rt_EscalaDao = new Rt_EscalaDao();
            EscalaDao escalaDao = new EscalaDao();

            //Inserindo campos
            rotasDao.atualizarRota(rotas, persistencia);

            //Atualizando em Rt_Escala
            rt_Escala.setHora(rotas.getHrLargada());
            rt_Escala.setOperador(rotas.getOperador());
            rt_Escala.setDt_alter(tratarValores(rotas.getDt_Alter().toString()));
            rt_Escala.setHr_alter(rotas.getHr_Alter());
            rt_Escala.setSequencia(rotas.getSequencia());
            rt_Escala = (Rt_Escala) FuncoesString.removeAcentoObjeto(rt_Escala);
            rt_EscalaDao.atualizarEscala(rt_Escala, persistencia);

            //Criando escala Escala
            escala.setSeqRota(rotas.getSequencia().toPlainString());
            escala.setData(rotas.getData());
            escala.setHora1(rotas.getHrLargada());
            escala.setHora2(rotas.getHrIntIni());
            escala.setHora3(rotas.getHrIntFim());
            escala.setHora4(rotas.getHrChegada());
            escala.setHsTot(rotas.getHsTotal().toPlainString());
            escala.setOperador(rotas.getOperador());
            escala.setDt_Alter(rotas.getDt_Alter());
            escala.setHr_Alter(rotas.getHr_Alter());
            escala = (Escala) FuncoesString.removeAcentoObjeto(escala);
            escalaDao.atualizarEscalaSupervisaoRotaSatMobWeb(escala, tratarValores(rotas.getData()), persistencia);

        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void atualizarValorParada(Rt_Perc rt_Perc, BigDecimal valor, Persistencia persistencia) throws Exception {
        try {
            Rt_PercDao rt_PercDao = new Rt_PercDao();
            rt_PercDao.atualizaValorParada(persistencia, rt_Perc.getSequencia().toString(), valor.toString(), String.valueOf(rt_Perc.getParada()));
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Atualiza o trajeto com outras informações
     *
     * @param rt_Perc
     * @param persistencia
     * @throws Exception
     */
    public void atualizarTrajeto(Rt_Perc rt_Perc, Persistencia persistencia) throws Exception {
        try {
            Rt_PercDao rt_PercDao = new Rt_PercDao();
            rt_Perc.setHora1(rt_Perc.getHora1().replace(":", ""));
            rt_Perc.setHora1D(rt_Perc.getHora1D().replace(":", ""));
            rt_PercDao.atualizarTrajeto(rt_Perc, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Cria os trajetos
     *
     * @param rt_Perc Objeto contedo todos os registros do trajeto
     * @param persistencia conexão com o banco de dados
     * @return código da parada
     * @throws Exception
     */
    public int criarTrajetos(Rt_Perc rt_Perc, Persistencia persistencia) throws Exception {
        try {
            Rt_PercDao percDao = new Rt_PercDao();
            int parada = 0;
            for (int i = 0; i < 20; i++) {
                try {
                    //Criando um novo
                    parada = percDao.getMaxParada(rt_Perc.getSequencia(), persistencia) + 1;
                    rt_Perc.setParada(parada);
                    rt_Perc.setHora1(rt_Perc.getHora1().replace(":", ""));
                    rt_Perc.setHora1D(rt_Perc.getHora1D().replace(":", ""));
                    rt_Perc.setDt_Alter(LocalDate.now());
                    rt_Perc.setDt_Incl(LocalDate.now());
                    rt_Perc.setHr_Alter(getDataAtual("HORA"));
                    rt_Perc = (Rt_Perc) FuncoesString.removeAcentoObjeto(rt_Perc);
                    percDao.inserirTrajetosSatMobWeb(rt_Perc, persistencia);
                } catch (Exception e) {
                    parada = 0;
                } finally {
                    if (parada != 0) {
                        break;
                    }
                }
            }
            if (parada == 0) {
                throw new Exception("TentativasExcedidas");
            }
            return parada;
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Retorna o código de caixa forte da filial
     *
     * @param codFil
     * @param persistencia
     * @return
     * @throws Exception
     */
    public String cxForte(BigDecimal codFil, Persistencia persistencia) throws Exception {
        try {
            CxForteDao cxForteDao = new CxForteDao();
            return cxForteDao.getCxForte(codFil, persistencia).getCodCli();
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Edita trajetos da rota
     *
     * @param rt_Perc Objeto de rt_parc
     * @param persistencia conexão com o banco de dados
     * @throws Exception
     */
    public void editarTrajeto(Rt_Perc rt_Perc, Persistencia persistencia) throws Exception {
        try {
            Rt_PercDao percDao = new Rt_PercDao();

            //Criando data e hora de alteração
            LocalTime hora = LocalTime.now();
            LocalDate data = LocalDate.now();
            rt_Perc.setDt_Alter(data);
            rt_Perc.setHr_Alter(hora.getHour() + ":" + hora.getMinute());
            rt_Perc = (Rt_Perc) FuncoesString.removeAcentoObjeto(rt_Perc);
            percDao.atualizarTrajetosSatMobWeb(rt_Perc, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    //Realiza o tratamento de valores
    private String tratarValores(String valores) {
        if (valores.contains("-")) {
            valores = valores.replace("-", "");
        }
        return valores;
    }

    public String MaxRota(String Codfil, LocalDate data, Persistencia persistencia) throws Exception {
        String retorno;
        try {
            RotasDao rotasDao = new RotasDao();
            retorno = rotasDao.proximaRotaSupervisao(Codfil, data, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
        return retorno;
    }

    public String proximaRotaTransporte(String codfil, String data, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            return rotasDao.proximaRotaTransporte(codfil, data, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Escala> SelecionaEscalas(String data, BigDecimal codpessoa, BigDecimal codfil, Persistencia persistencia) throws Exception {
        List<Escala> escalas = new ArrayList<>();
        EscalaDao escalaDao = new EscalaDao();
        try {
            escalas = escalaDao.VerficaEscala(data, codpessoa, codfil, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }

        return escalas;
    }

    //Seleciona Rt_Escala, Escala e Rotas a partir da sequencia
    public Escala selecionaEscala(BigDecimal seqrota, Persistencia persistencia) throws Exception {
        try {
            EscalaDao escalaDao = new EscalaDao();
            return escalaDao.getEscala(seqrota, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Rt_Escala SelecionaRt_Escala(BigDecimal sequencia, Persistencia persistencia) throws Exception {
        Rt_Escala retorno = new Rt_Escala();
        Rt_EscalaDao rt_EscalaDao = new Rt_EscalaDao();
        try {
            retorno = rt_EscalaDao.SelecionaRt_EscalaSatMobWeb(sequencia, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }

        return retorno;
    }

    //Seleciona a ultima sequecina preenchida
    public Rotas SelecionaUltimaRota(BigDecimal codfil, LocalDate date, Persistencia persistencia) throws Exception {
        Rotas retorno = new Rotas();
        BigDecimal sequencia = null;
        RotasDao rotasDao = new RotasDao();

        try {
            sequencia = rotasDao.SelecionaUltimaSequenciaSatMobWeb(codfil, date, persistencia);
            retorno = rotasDao.SelecionaUltimaRotaSatMobWeb(sequencia, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
        return retorno;
    }

    /**
     * Incluir uma flag de exclusão no registros
     *
     * @param rt_Perc
     * @param persistencia
     * @throws Exception
     */
    public void FlagExcl(Rt_Perc rt_Perc, Persistencia persistencia) throws Exception {
        Rt_PercDao rt_PercDao = new Rt_PercDao();
        try {
            rt_PercDao.ExcluirParadaSatMobWeb(rt_Perc, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Exclui permanentemente do banco
     *
     * @param rotas Rota a seer excluída
     * @param persistencia Conexão com o banco de dados
     * @throws Exception
     */
    public void excluirRota(Rotas rotas, Persistencia persistencia) throws Exception {
        RotasDao rotasDao = new RotasDao();
        Rt_PercDao rt_PercDao = new Rt_PercDao();
        EscalaDao escalaDao = new EscalaDao();
        try {
            rotasDao.excluir(rotas, persistencia);

            List<Rt_Perc> rt_Perc = rt_PercDao.listarTrajetos(rotas.getSequencia(), persistencia);

            for (Rt_Perc rt_Perc1 : rt_Perc) {
                rt_Perc1.setOperExcl(rotas.getOperFech());
                rt_Perc1.setHr_Excl(rotas.getHr_Fech());
                rt_Perc1.setDt_Excl(rotas.getDt_Fech());
                rt_PercDao.ExcluirParadasRotaSatMobWeb(rt_Perc1, persistencia);
            }

            escalaDao.ExcluirEscalaSatMobWeb(rotas.getSequencia(), persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public void excluirParada(String sequencia, int parada, Persistencia persistencia) {

    }

    public Rotas BuscaRota(String codfil, String rotas, String data, Persistencia persistencia) throws Exception {
        Rotas rota = new Rotas();
        RotasDao rotasDao = new RotasDao();
        try {
            rota = rotasDao.buscaRota(codfil, rotas, data, persistencia);

        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
        return rota;
    }

    /* LISTAGENS PAGINADAS */
    /**
     * Contagem do cadastro de supervisoes
     *
     * @param filtros - filtros para pesquisa
     * @param codPessoa - codigo de pessoa do usuário
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public Integer Contagem(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            RotasDao rotasDao = new RotasDao();
            retorno = rotasDao.TotalRotasMobWeb(filtros, codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    /**
     * Listagem do cadastro de supervisoes
     *
     * @param primeiro - número do primeiro elemento
     * @param linhas - número de linhas por página
     * @param filtros - filtros de pesquisa
     * @param codPessoa - codigo de pessoa do usuario
     * @param persistencia conexão ao banco de dados
     * @return
     * @throws Exception
     */
    public List<Rotas> ListagemPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {

        try {
            List<Rotas> retorno;
            RotasDao rotasDao = new RotasDao();
            retorno = rotasDao.listaPaginada(primeiro, linhas, filtros, codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Rotas> listagemValoresPaginada(int primeiro, int linhas, Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            try {
                return rotasDao.listaPaginadaValoresDtFim(primeiro, linhas, filtros, codPessoa, persistencia);
            } catch (Exception e) {
                return rotasDao.listaPaginadaValores(primeiro, linhas, filtros, codPessoa, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Rotas> listagemValoresPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            try {
                return rotasDao.listaPaginadaValoresDtFim(primeiro, linhas, filtros, persistencia);
            } catch (Exception e) {
                return rotasDao.listaPaginadaValores(primeiro, linhas, filtros, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Rotas> listagemRotasContainersPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            try {
                return rotasDao.listaPaginadaSPMValoresDtFim(primeiro, linhas, filtros, persistencia);
            } catch (Exception e) {
                return rotasDao.listaPaginadaValores(primeiro, linhas, filtros, persistencia);
            }
        } catch (Exception e) {
            throw new Exception("RotasSPM.falhageral<message>" + e.getMessage());
        }
    }

    public Integer contagemRotasContainers(Map filtros, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            RotasDao rotasDao = new RotasDao();
            retorno = rotasDao.totalSPMRotasValores(filtros, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasSPM.falhageral<message>" + e.getMessage());
        }
    }

    public List<Pedido> listagemPedidosContainersPaginada(int primeiro, int linhas, Map filtros, Persistencia persistencia) throws Exception {
        try {
            PedidoDao pedidoDao = new PedidoDao();
            return pedidoDao.listapaginada(primeiro, linhas, filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSPM.falhageral<message>" + e.getMessage());
        }
    }

    public Integer contagemPedidosContainers(Map filtros, Persistencia persistencia) throws Exception {
        try {
            PedidoDao pedidoDao = new PedidoDao();
            return pedidoDao.totalPedidosMobWeb(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSPM.falhageral<message>" + e.getMessage());
        }
    }

    public Integer contagemValores(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            int retorno;
            RotasDao rotasDao = new RotasDao();
            retorno = rotasDao.totalRotasValores(filtros, codPessoa, persistencia);
            return retorno;
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Integer contagemValores(Map filtros, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            return rotasDao.totalRotasValores(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public BigDecimal somaValores(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            return rotasDao.totalValores(filtros, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public BigDecimal somaValoresSemCodPessoa(Map filtros, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            return rotasDao.totalValoresSemCodPessoa(filtros, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Rotas> detalhesRota(Map filtros, BigDecimal codPessoa, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            return rotasDao.listaPaginadaValores(0, 1000, filtros, codPessoa, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Rastrear> posicoes(String data, String codFil, Persistencia persistencia) throws Exception {
        try {
            EscalaDao escalaDao = new EscalaDao();
            List<Escala> rotas = escalaDao.listaRotasValores(data, codFil, persistencia);
            if (rotas.isEmpty()) {
                return new ArrayList<>();
            }
            RastrearDao rastrearDao = new RastrearDao();
            return rastrearDao.buscarLocalizacoes(rotas, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Rastrear posicao(String sequencia, String codFil, Persistencia persistencia) throws Exception {
        try {
            RastrearDao rastrearDao = new RastrearDao();
            return rastrearDao.buscarLocalizacao(sequencia, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public Rastrear PosicaoCental(String codFil, Persistencia persistencia) throws Exception {
        try {
            RastrearDao rastrearDao = new RastrearDao();
            return rastrearDao.posicaoCentral(codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Rt_Perc> relatorioProdutividade(String data, String codFil, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            return rotasDao.relatorioProdutividade(data, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Rt_Perc> detalhesRotasData(String data, String codFil, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            return rotasDao.detalhesRotasData(data, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Rt_Perc> resultadoRoteiros(String data, String codFil, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            return rotasDao.resultadoRoteiros(data, codFil, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Rt_Perc> preFatura(String data, String codFil, BigDecimal codPessoa, String portal, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            return rotasDao.preFaturaRoteiros(data, codFil, codPessoa, portal, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public List<Rt_Perc> preFaturaCliFat(String dtIni, String dtFim, String codFil, BigDecimal codPessoa, String portal, Persistencia persistencia) throws Exception {
        try {
            RotasDao rotasDao = new RotasDao();
            return rotasDao.preFaturaCliFatData(dtIni, dtFim, codFil, codPessoa, portal, persistencia);
        } catch (Exception e) {
            throw new Exception("RotasSatWeb.falhageral<message>" + e.getMessage());
        }
    }

    public boolean verificaRotaModelo(String codFil, String data, Persistencia persistencia) throws Exception {
        RotasDao rotasDao = new RotasDao();
        return rotasDao.verificaRotaModelo(codFil, data, persistencia);
    }

    public boolean validarModeloRotaExistente(String data, String codFil, Persistencia persistencia) throws Exception {
        RotasDao rotasDao = new RotasDao();
        return rotasDao.validarModeloRotaExistente(data, codFil, persistencia);
    }

    public boolean validarModeloExistente(String modelo, String codFil, Persistencia persistencia) throws Exception {
        RotasDao rotasDao = new RotasDao();
        return rotasDao.validarModeloExistente(modelo, codFil, persistencia);
    }

    public int inserirRotaModelo(
            String data,
            String modelo,
            String operador,
            String dt_alter,
            String hr_alter,
            Persistencia persistencia
    ) throws Exception {
        RotasDao rotasDao = new RotasDao();
        return rotasDao.inserirRotaModelo(data, modelo, operador, dt_alter, hr_alter, persistencia);
    }

    public int inserirModeloRota(
            String data,
            String codFil,
            String modelo,
            String operador,
            String dt_alter,
            String hr_alter,
            Persistencia persistencia
    ) throws Exception {
        RotasDao rotasDao = new RotasDao();
        return rotasDao.inserirModeloRota(data, codFil, modelo, operador, dt_alter, hr_alter, persistencia);
    }

    public int gerarPedidoUsandoFrequencia(
            String data,
            String codFil,
            String operador,
            String dt_alter,
            String hr_alter,
            String tolerancia,
            Persistencia persistencia
    ) throws Exception {
        RotasDao rotasDao = new RotasDao();
        return rotasDao.gerarPedidoUsandoFrequencia(data, codFil, operador, dt_alter, hr_alter, tolerancia, persistencia);
    }

    public int gerarSaidasAutomaticas(
            String data,
            String codFil,
            String operador,
            String dt_alter,
            String hr_alter,
            Persistencia persistencia
    ) throws Exception {
        RotasDao rotasDao = new RotasDao();
        return rotasDao.gerarSaidasAutomaticas(data, codFil, operador, dt_alter, hr_alter, persistencia);
    }
}
